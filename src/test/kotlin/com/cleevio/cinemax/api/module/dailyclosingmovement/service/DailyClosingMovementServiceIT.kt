package com.cleevio.cinemax.api.module.dailyclosingmovement.service

import com.cleevio.cinemax.api.DatabaseCleanup
import com.cleevio.cinemax.api.IntegrationTest
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumRepository
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutRepository
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.service.BasketRepository
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemRepository
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.exception.DailyClosingNotFoundException
import com.cleevio.cinemax.api.module.dailyclosing.exception.InvalidDailyClosingPosConfigurationException
import com.cleevio.cinemax.api.module.dailyclosing.exception.InvalidDailyClosingStateException
import com.cleevio.cinemax.api.module.dailyclosing.service.DailyClosingRepository
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemType
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementType
import com.cleevio.cinemax.api.module.dailyclosingmovement.event.CashMovementCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.dailyclosingmovement.exception.DailyClosingMovementNotFoundException
import com.cleevio.cinemax.api.module.dailyclosingmovement.exception.InvalidDailyClosingMovementItemSubtypeException
import com.cleevio.cinemax.api.module.dailyclosingmovement.exception.InvalidDailyClosingMovementItemTypeException
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.CreateOrUpdateBaseDailyClosingMovementsCommand
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.CreateOrUpdateDeductionDailyClosingMovementCommand
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.DeleteOtherDailyClosingMovementCommand
import com.cleevio.cinemax.api.module.distributor.service.DistributorRepository
import com.cleevio.cinemax.api.module.movie.service.MovieRepository
import com.cleevio.cinemax.api.module.posconfiguration.constant.PosConfigurationType
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationRepository
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryRepository
import com.cleevio.cinemax.api.module.reservation.service.ReservationRepository
import com.cleevio.cinemax.api.module.screening.service.ScreeningRepository
import com.cleevio.cinemax.api.module.seat.service.SeatRepository
import com.cleevio.cinemax.api.module.ticket.service.TicketRepository
import com.cleevio.cinemax.api.module.ticketprice.service.TicketPriceRepository
import com.cleevio.cinemax.api.util.copyBasketItem
import com.cleevio.cinemax.api.util.createAuditorium
import com.cleevio.cinemax.api.util.createAuditoriumLayout
import com.cleevio.cinemax.api.util.createBasket
import com.cleevio.cinemax.api.util.createBasketItem
import com.cleevio.cinemax.api.util.createDailyClosing
import com.cleevio.cinemax.api.util.createDailyClosingMovement
import com.cleevio.cinemax.api.util.createDailyClosingMovementBaseGroupSet
import com.cleevio.cinemax.api.util.createDistributor
import com.cleevio.cinemax.api.util.createMovie
import com.cleevio.cinemax.api.util.createPosConfiguration
import com.cleevio.cinemax.api.util.createPriceCategory
import com.cleevio.cinemax.api.util.createReservation
import com.cleevio.cinemax.api.util.createScreening
import com.cleevio.cinemax.api.util.createSeat
import com.cleevio.cinemax.api.util.createTicket
import com.cleevio.cinemax.api.util.createTicketPrice
import com.cleevio.cinemax.api.util.mapDailyClosingMovementToCreateOtherDailyClosingMovementCommand
import com.cleevio.cinemax.api.util.mapDailyClosingMovementToUpdateOtherDailyClosingMovementCommand
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import org.junit.jupiter.api.BeforeEach
import org.junit.jupiter.api.Test
import org.junit.jupiter.api.assertThrows
import org.springframework.beans.factory.annotation.Autowired
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID
import kotlin.jvm.optionals.getOrNull
import kotlin.test.assertEquals
import kotlin.test.assertNotNull
import kotlin.test.assertNull
import kotlin.test.assertTrue

class DailyClosingMovementServiceIT @Autowired constructor(
    private val underTest: DailyClosingMovementService,
    private val dailyClosingMovementRepository: DailyClosingMovementRepository,
    private val dailyClosingRepository: DailyClosingRepository,
    private val posConfigurationRepository: PosConfigurationRepository,
    private val basketRepository: BasketRepository,
    private val basketItemRepository: BasketItemRepository,
    private val screeningRepository: ScreeningRepository,
    private val ticketRepository: TicketRepository,
    private val ticketPriceRepository: TicketPriceRepository,
    private val reservationRepository: ReservationRepository,
    private val movieRepository: MovieRepository,
    private val auditoriumRepository: AuditoriumRepository,
    private val distributorRepository: DistributorRepository,
    private val priceCategoryRepository: PriceCategoryRepository,
    private val auditoriumLayoutRepository: AuditoriumLayoutRepository,
    private val seatRepository: SeatRepository,
) : IntegrationTest(DatabaseCleanup.AFTER_EACH) {

    @BeforeEach
    fun beforeEach() {
        auditoriumRepository.save(AUDITORIUM)
        auditoriumLayoutRepository.save(AUDITORIUM_LAYOUT)
        seatRepository.saveAll(listOf(SEAT_1, SEAT_2))
        distributorRepository.save(DISTRIBUTOR)
        priceCategoryRepository.save(PRICE_CATEGORY)
        movieRepository.save(MOVIE)
        screeningRepository.save(SCREENING)
        reservationRepository.saveAll(listOf(RESERVATION_1, RESERVATION_2))
        ticketPriceRepository.saveAll(listOf(TICKET_PRICE_1, TICKET_PRICE_2))
        ticketRepository.saveAll(listOf(TICKET_1, TICKET_2))
        posConfigurationRepository.save(POS_CONFIGURATION_1)
        basketRepository.saveAll(listOf(BASKET_1, BASKET_2))
        basketItemRepository.saveAll(ALL_BASKET_ITEMS)

        every { applicationEventPublisherMock.publishEvent(any<CashMovementCreatedOrUpdatedEvent>()) } just Runs
    }

    @Test
    fun `test createOrUpdateBaseMovements - base movements do not exist - should create set of new ones with correctly calculated amounts`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)

        assertTrue { dailyClosingMovementRepository.findAll().isEmpty() }

        underTest.createOrUpdateBaseMovements(
            CreateOrUpdateBaseDailyClosingMovementsCommand(
                dailyClosingId = DAILY_CLOSING_3.id,
                basketItemIds = ALL_BASKET_ITEMS.map { it.id }.toSet()
            )
        )

        val dailyClosingMovements = dailyClosingMovementRepository.findAll()

        assertEquals(10, dailyClosingMovements.size)

        // TICKETS_CASH
        dailyClosingMovements[0].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.TICKETS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.TICKET_SALES, it.itemSubtype)
            assertEquals(DailyClosingMovementType.REVENUE, it.type)
            assertEquals(PaymentType.CASH, it.paymentType)
            assertEquals("P000000001", it.receiptNumber)
            assertEquals("3".toBigDecimal(), it.amount) // 12 (total price) - (1+3+5) (fees)
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }

        // TICKETS_CASHLESS
        dailyClosingMovements[1].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.TICKETS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.TICKET_SALES, it.itemSubtype)
            assertEquals(DailyClosingMovementType.REVENUE, it.type)
            assertEquals(PaymentType.CASHLESS, it.paymentType)
            assertEquals("1.5".toBigDecimal(), it.amount) // 6 (total price) - (0.5+1.5+2.5) (fees)
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }

        // TICKETS_SERVICE_FEES_CASH
        dailyClosingMovements[2].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.TICKETS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.SERVICE_FEES_SALES, it.itemSubtype)
            assertEquals(DailyClosingMovementType.REVENUE, it.type)
            assertEquals(PaymentType.CASH, it.paymentType)
            assertEquals("P000000003", it.receiptNumber)
            assertEquals("9".toBigDecimal(), it.amount) // sum of all fees (1+3+5)
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }

        // TICKETS_SERVICE_FEES_CASHLESS
        dailyClosingMovements[3].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.TICKETS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.SERVICE_FEES_SALES, it.itemSubtype)
            assertEquals(DailyClosingMovementType.REVENUE, it.type)
            assertEquals(PaymentType.CASHLESS, it.paymentType)
            assertEquals("4.5".toBigDecimal(), it.amount) // sum of all fees (0.5+1.5+2.5)
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }

        // PRODUCTS_CASH
        dailyClosingMovements[4].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.PRODUCTS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.PRODUCT_SALES, it.itemSubtype)
            assertEquals(DailyClosingMovementType.REVENUE, it.type)
            assertEquals(PaymentType.CASH, it.paymentType)
            assertEquals("17.5".toBigDecimal(), it.amount) // 23 (product price) + (-5.5) (discount)
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }

        // PRODUCTS_CASHLESS
        dailyClosingMovements[5].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.PRODUCTS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.PRODUCT_SALES, it.itemSubtype)
            assertEquals(DailyClosingMovementType.REVENUE, it.type)
            assertEquals(PaymentType.CASHLESS, it.paymentType)
            assertEquals("22.75".toBigDecimal(), it.amount) // 25 (product price) + (-2.25) (discount)
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }

        // TICKETS_CANCELLED_CASH
        dailyClosingMovements[6].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.TICKETS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.CANCELLED_TICKETS, it.itemSubtype)
            assertEquals(DailyClosingMovementType.EXPENSE, it.type)
            assertEquals(PaymentType.CASH, it.paymentType)
            assertEquals("8".toBigDecimal(), it.amount) // Basket item price for cancelled ticket
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }

        // TICKETS_CANCELLED_CASHLESS
        dailyClosingMovements[7].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.TICKETS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.CANCELLED_TICKETS, it.itemSubtype)
            assertEquals(DailyClosingMovementType.EXPENSE, it.type)
            assertEquals(PaymentType.CASHLESS, it.paymentType)
            assertEquals("4".toBigDecimal(), it.amount) // Basket item price for cancelled ticket
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }

        // PRODUCTS_CANCELLED_CASH
        dailyClosingMovements[8].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.PRODUCTS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.CANCELLED_PRODUCTS, it.itemSubtype)
            assertEquals(DailyClosingMovementType.EXPENSE, it.type)
            assertEquals(PaymentType.CASH, it.paymentType)
            assertEquals(
                "10.5".toBigDecimal(),
                it.amount
            ) // 13 (cancelled product price) + (-2.5) (cancelled discount)
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }

        // PRODUCTS_CANCELLED_CASHLESS
        dailyClosingMovements[9].let {
            assertNotNull(it.id)
            assertEquals(DAILY_CLOSING_3.id, it.dailyClosingId)
            assertEquals(DailyClosingMovementItemType.PRODUCTS, it.itemType)
            assertEquals(DailyClosingMovementItemSubtype.CANCELLED_PRODUCTS, it.itemSubtype)
            assertEquals(DailyClosingMovementType.EXPENSE, it.type)
            assertEquals(PaymentType.CASHLESS, it.paymentType)
            assertEquals(
                "13.5".toBigDecimal(),
                it.amount
            ) // 15 (cancelled product price) + (-1.5) (cancelled discount)
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }
    }

    @Test
    fun `test createOrUpdateBaseMovements - base movements exist - should just update them with newly calculated amounts`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)
        dailyClosingMovementRepository.saveAll(createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_3.id))
        val originalDailyClosingMovements = dailyClosingMovementRepository.findAll().sortedBy { it.createdAt }

        assertEquals(10, originalDailyClosingMovements.size)
        assertEquals(BigDecimal.ZERO, originalDailyClosingMovements.map { it.amount }.reduce { a, b -> a + b })

        underTest.createOrUpdateBaseMovements(
            CreateOrUpdateBaseDailyClosingMovementsCommand(
                dailyClosingId = DAILY_CLOSING_3.id,
                basketItemIds = ALL_BASKET_ITEMS.map { it.id }.toSet()
            )
        )

        val updatedDailyClosingMovements = dailyClosingMovementRepository.findAll().sortedBy { it.createdAt }

        assertEquals(10, updatedDailyClosingMovements.size)
        assertEquals(10, originalDailyClosingMovements.size)
        assertEquals(94.25.toBigDecimal(), updatedDailyClosingMovements.map { it.amount }.reduce { a, b -> a + b })
        updatedDailyClosingMovements.forEachIndexed { index, it ->
            val origMovement = originalDailyClosingMovements[index]
            assertEquals(origMovement.id, it.id)
            assertEquals(origMovement.dailyClosingId, it.dailyClosingId)
            assertEquals(origMovement.itemType, it.itemType)
            assertEquals(origMovement.itemSubtype, it.itemSubtype)
            assertEquals(origMovement.type, it.type)
            assertEquals(origMovement.paymentType, it.paymentType)
            assertNull(it.otherReceiptNumber)
            assertNull(it.title)
            assertNull(it.variableSymbol)
        }
    }

    @Test
    fun `test createOrUpdateBaseMovements - base movements exist but not all required - should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)
        dailyClosingMovementRepository.saveAll(
            createDailyClosingMovementBaseGroupSet(DAILY_CLOSING_3.id).filter { it.paymentType == PaymentType.CASH }
        )

        val originalDailyClosingMovements = dailyClosingMovementRepository.findAll()

        assertEquals(5, originalDailyClosingMovements.size)

        assertThrows<IllegalStateException> {
            underTest.createOrUpdateBaseMovements(
                CreateOrUpdateBaseDailyClosingMovementsCommand(
                    dailyClosingId = DAILY_CLOSING_3.id,
                    basketItemIds = setOf()
                )
            )
        }
    }

    @Test
    fun `test createOrUpdateBaseMovements - daily closing does not exist - should throw`() {
        assertThrows<DailyClosingNotFoundException> {
            underTest.createOrUpdateBaseMovements(
                CreateOrUpdateBaseDailyClosingMovementsCommand(
                    dailyClosingId = UUID.fromString("e46ce29e-cb34-46ce-a9f9-07f14446dde4"),
                    basketItemIds = setOf()
                )
            )
        }
    }

    @Test
    fun `test createOrUpdateBaseMovements - daily closing is not open - should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_1)
        assertThrows<InvalidDailyClosingStateException> {
            underTest.createOrUpdateBaseMovements(
                CreateOrUpdateBaseDailyClosingMovementsCommand(
                    dailyClosingId = DAILY_CLOSING_1.id,
                    basketItemIds = setOf()
                )
            )
        }
    }

    @Test
    fun `test createOtherMovement - command with invalid item type - should throw`() {
        assertThrows<InvalidDailyClosingMovementItemTypeException> {
            underTest.createOtherMovement(
                mapDailyClosingMovementToCreateOtherDailyClosingMovementCommand(
                    OTHER_DAILY_CLOSING_MOVEMENT
                ).copy(itemType = DailyClosingMovementItemType.DEDUCTION)
            )
        }
    }

    @Test
    fun `test createOtherMovement - PosConfiguration is of ONLINE type - should throw`() {
        posConfigurationRepository.save(POS_CONFIGURATION_2)
        dailyClosingRepository.save(DAILY_CLOSING_4)

        assertThrows<InvalidDailyClosingPosConfigurationException> {
            underTest.createOtherMovement(
                mapDailyClosingMovementToCreateOtherDailyClosingMovementCommand(
                    createDailyClosingMovement(
                        dailyClosingId = DAILY_CLOSING_4.id,
                        title = "Other online movement",
                        itemType = DailyClosingMovementItemType.TICKETS,
                        itemSubtype = DailyClosingMovementItemSubtype.OTHER,
                        variableSymbol = "123456",
                        receiptNumber = "ONL0000001",
                        amount = 50.toBigDecimal()
                    )
                )
            )
        }
    }

    @Test
    fun `test createOtherMovement - daily closing does not exist - should throw`() {
        assertThrows<DailyClosingNotFoundException> {
            underTest.createOtherMovement(
                mapDailyClosingMovementToCreateOtherDailyClosingMovementCommand(
                    OTHER_DAILY_CLOSING_MOVEMENT
                ).copy(dailyClosingId = UUID.fromString("4105d0c1-4738-4005-af6f-0927a8b26850"))
            )
        }
    }

    @Test
    fun `test createOtherMovement - daily closing exists but it is not OPEN - should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_1)
        assertThrows<InvalidDailyClosingStateException> {
            underTest.createOtherMovement(
                mapDailyClosingMovementToCreateOtherDailyClosingMovementCommand(
                    OTHER_DAILY_CLOSING_MOVEMENT
                ).copy(dailyClosingId = DAILY_CLOSING_1.id)
            )
        }
    }

    @Test
    fun `test updateOtherMovement - command with invalid item type - should throw`() {
        assertThrows<InvalidDailyClosingMovementItemTypeException> {
            underTest.updateOtherMovement(
                mapDailyClosingMovementToUpdateOtherDailyClosingMovementCommand(
                    OTHER_DAILY_CLOSING_MOVEMENT
                ).copy(itemType = DailyClosingMovementItemType.DEDUCTION)
            )
        }
    }

    @Test
    fun `test updateOtherMovement - daily closing does not exist - should throw`() {
        assertThrows<DailyClosingNotFoundException> {
            underTest.updateOtherMovement(
                mapDailyClosingMovementToUpdateOtherDailyClosingMovementCommand(
                    OTHER_DAILY_CLOSING_MOVEMENT
                ).copy(dailyClosingId = UUID.fromString("4105d0c1-4738-4005-af6f-0927a8b26850"))
            )
        }
    }

    @Test
    fun `test updateOtherMovement - daily closing exists but it is not OPEN - should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_1)
        assertThrows<InvalidDailyClosingStateException> {
            underTest.updateOtherMovement(
                mapDailyClosingMovementToUpdateOtherDailyClosingMovementCommand(
                    OTHER_DAILY_CLOSING_MOVEMENT
                ).copy(dailyClosingId = DAILY_CLOSING_1.id)
            )
        }
    }

    @Test
    fun `test updateOtherMovement - valid update command but movement does not exists - should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)

        assertThrows<DailyClosingMovementNotFoundException> {
            underTest.updateOtherMovement(
                mapDailyClosingMovementToUpdateOtherDailyClosingMovementCommand(
                    OTHER_DAILY_CLOSING_MOVEMENT
                ).copy(
                    id = UUID.fromString("3d5da631-ad73-458c-a134-0532ca891ee9")
                )
            )
        }
    }

    @Test
    fun `test updateOtherMovement - command to update but movement is not of OTHER subtype - should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)
        dailyClosingMovementRepository.save(TICKET_SALES_DAILY_CLOSING_MOVEMENT)

        assertThrows<InvalidDailyClosingMovementItemSubtypeException> {
            underTest.updateOtherMovement(
                mapDailyClosingMovementToUpdateOtherDailyClosingMovementCommand(
                    TICKET_SALES_DAILY_CLOSING_MOVEMENT
                )
            )
        }
    }

    @Test
    fun `test createOtherMovement - valid command - should correctly create a new other movement`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)
        assertTrue(dailyClosingMovementRepository.findAll().isEmpty())

        underTest.createOtherMovement(
            mapDailyClosingMovementToCreateOtherDailyClosingMovementCommand(
                OTHER_DAILY_CLOSING_MOVEMENT
            )
        )

        val dailyClosings = dailyClosingMovementRepository.findAll()
        assertEquals(1, dailyClosings.size)
        dailyClosings[0].let {
            assertNotNull(it.id)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.dailyClosingId, it.dailyClosingId)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.title, it.title)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.itemType, it.itemType)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.itemSubtype, it.itemSubtype)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.paymentType, it.paymentType)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.variableSymbol, it.variableSymbol)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.receiptNumber, it.receiptNumber)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.otherReceiptNumber, it.otherReceiptNumber)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.amount, it.amount)
        }
    }

    @Test
    fun `test updateOtherMovement - command to update - should correctly update existing other movement`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)
        dailyClosingMovementRepository.save(OTHER_DAILY_CLOSING_MOVEMENT)
        dailyClosingMovementRepository.findAll().let {
            assertEquals(1, it.size)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.id, it[0].id)
        }

        val updateCommand = mapDailyClosingMovementToUpdateOtherDailyClosingMovementCommand(
            OTHER_DAILY_CLOSING_MOVEMENT
        ).copy(
            type = DailyClosingMovementType.EXPENSE,
            itemType = DailyClosingMovementItemType.PRODUCTS,
            paymentType = PaymentType.CASHLESS,
            title = "Updated other movement",
            variableSymbol = "789012",
            otherReceiptNumber = "OUT0000001",
            amount = 51.toBigDecimal()
        )

        underTest.updateOtherMovement(updateCommand)

        val updatedDailyClosings = dailyClosingMovementRepository.findAll()
        assertEquals(1, updatedDailyClosings.size)
        updatedDailyClosings[0].let {
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.id, it.id)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.dailyClosingId, it.dailyClosingId)
            assertEquals(updateCommand.title, it.title)
            assertEquals(updateCommand.itemType, it.itemType)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.itemSubtype, it.itemSubtype)
            assertEquals(updateCommand.paymentType, it.paymentType)
            assertEquals(updateCommand.variableSymbol, it.variableSymbol)
            assertEquals(OTHER_DAILY_CLOSING_MOVEMENT.receiptNumber, it.receiptNumber)
            assertEquals(updateCommand.otherReceiptNumber, it.otherReceiptNumber)
            assertEquals(updateCommand.amount, it.amount)
        }
    }

    @Test
    fun `test deleteOtherMovement - daily closing does not exist - should throw`() {
        assertThrows<DailyClosingNotFoundException> {
            underTest.deleteOtherMovement(
                DeleteOtherDailyClosingMovementCommand(
                    dailyClosingMovementId = OTHER_DAILY_CLOSING_MOVEMENT.id,
                    dailyClosingId = UUID.fromString("e46ce29e-cb34-46ce-a9f9-07f14446dde4")
                )
            )
        }
    }

    @Test
    fun `test deleteOtherMovement - daily closing exists but it is not OPEN - should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_1)
        assertThrows<InvalidDailyClosingStateException> {
            underTest.deleteOtherMovement(
                DeleteOtherDailyClosingMovementCommand(
                    dailyClosingMovementId = OTHER_DAILY_CLOSING_MOVEMENT.id,
                    dailyClosingId = DAILY_CLOSING_1.id
                )
            )
        }
    }

    @Test
    fun `test deleteOtherMovement - movement exists but is not of OTHER subtype - should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)
        dailyClosingMovementRepository.save(TICKET_SALES_DAILY_CLOSING_MOVEMENT)

        assertThrows<InvalidDailyClosingMovementItemSubtypeException> {
            underTest.deleteOtherMovement(
                DeleteOtherDailyClosingMovementCommand(
                    dailyClosingMovementId = TICKET_SALES_DAILY_CLOSING_MOVEMENT.id,
                    dailyClosingId = DAILY_CLOSING_3.id
                )
            )
        }
    }

    @Test
    fun `test deleteOtherMovement - movement does not exist - should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)

        assertThrows<DailyClosingMovementNotFoundException> {
            underTest.deleteOtherMovement(
                DeleteOtherDailyClosingMovementCommand(
                    dailyClosingMovementId = UUID.fromString("4105d0c1-4738-4005-af6f-0927a8b26850"),
                    dailyClosingId = DAILY_CLOSING_3.id
                )
            )
        }
    }

    @Test
    fun `test deleteOtherMovement - valid command - should soft delete movement`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)
        dailyClosingMovementRepository.save(OTHER_DAILY_CLOSING_MOVEMENT)

        underTest.deleteOtherMovement(
            DeleteOtherDailyClosingMovementCommand(
                dailyClosingMovementId = OTHER_DAILY_CLOSING_MOVEMENT.id,
                dailyClosingId = DAILY_CLOSING_3.id
            )
        )

        val deletedMovement = dailyClosingMovementRepository.findById(OTHER_DAILY_CLOSING_MOVEMENT.id).getOrNull()
        assertNotNull(deletedMovement)
        assertNotNull(deletedMovement.deletedAt)
    }

    @Test
    fun `test createOrUpdateDeductionMovement - no daily closing exists, should throw`() {
        assertThrows<DailyClosingNotFoundException> {
            underTest.createOrUpdateDeductionMovement(
                CreateOrUpdateDeductionDailyClosingMovementCommand(
                    dailyClosingId = UUID.fromString("e46ce29e-cb34-46ce-a9f9-07f14446dde4")
                )
            )
        }
    }

    @Test
    fun `test createOrUpdateDeductionMovement - daily closing is not OPEN, should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_1)

        assertThrows<InvalidDailyClosingStateException> {
            underTest.createOrUpdateDeductionMovement(
                CreateOrUpdateDeductionDailyClosingMovementCommand(
                    dailyClosingId = DAILY_CLOSING_1.id
                )
            )
        }
    }

    @Test
    fun `test createOrUpdateDeductionMovement - dailyClosingCashBalance is lower than zero, should throw`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)
        dailyClosingMovementRepository.save(
            createDailyClosingMovement(
                dailyClosingId = DAILY_CLOSING_3.id,
                itemSubtype = DailyClosingMovementItemSubtype.OTHER,
                type = DailyClosingMovementType.EXPENSE,
                amount = "100".toBigDecimal()
            )
        )

        assertThrows<IllegalStateException> {
            underTest.createOrUpdateDeductionMovement(
                CreateOrUpdateDeductionDailyClosingMovementCommand(
                    dailyClosingId = DAILY_CLOSING_3.id
                )
            )
        }
    }

    @Test
    fun `test createOrUpdateDeductionMovement - deduction exists, should update amount correctly`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)
        val existingDeductionMovement = createDailyClosingMovement(
            dailyClosingId = DAILY_CLOSING_3.id,
            itemSubtype = DailyClosingMovementItemSubtype.DEDUCTION,
            itemType = DailyClosingMovementItemType.DEDUCTION,
            type = DailyClosingMovementType.EXPENSE,
            amount = "50".toBigDecimal()
        )
        val cashMovementPositiveBalance = createDailyClosingMovement(
            dailyClosingId = DAILY_CLOSING_3.id,
            itemSubtype = DailyClosingMovementItemSubtype.OTHER,
            type = DailyClosingMovementType.REVENUE,
            amount = "200".toBigDecimal()
        )

        dailyClosingMovementRepository.saveAll(setOf(existingDeductionMovement, cashMovementPositiveBalance))

        underTest.createOrUpdateDeductionMovement(
            CreateOrUpdateDeductionDailyClosingMovementCommand(
                dailyClosingId = DAILY_CLOSING_3.id
            )
        )

        val updatedDeductionMovement = dailyClosingMovementRepository.findById(existingDeductionMovement.id).get()
        assertEquals("200".toBigDecimal(), updatedDeductionMovement.amount)
    }

    @Test
    fun `test createOrUpdateDeductionMovement - deduction does not exist, should create new one with correct amount`() {
        dailyClosingRepository.save(DAILY_CLOSING_3)

        assertTrue(dailyClosingMovementRepository.findAll().isEmpty())

        dailyClosingMovementRepository.save(
            createDailyClosingMovement(
                dailyClosingId = DAILY_CLOSING_3.id,
                itemSubtype = DailyClosingMovementItemSubtype.OTHER,
                type = DailyClosingMovementType.REVENUE,
                amount = "200".toBigDecimal()
            )
        )

        underTest.createOrUpdateDeductionMovement(
            CreateOrUpdateDeductionDailyClosingMovementCommand(
                dailyClosingId = DAILY_CLOSING_3.id
            )
        )

        val dailyClosingMovements = dailyClosingMovementRepository.findAll()
        assertEquals(2, dailyClosingMovements.size)

        dailyClosingMovements.filter { it.itemSubtype == DailyClosingMovementItemSubtype.DEDUCTION }.let {
            assertEquals(1, it.size)
            assertEquals(200.toBigDecimal(), it[0].amount)
        }
    }
}

private val NOW = LocalDateTime.now()

// POS CONFIGURATIONS
private val POS_CONFIGURATION_1 = createPosConfiguration(
    macAddress = "AA:BB:CC:DD:EE",
    type = PosConfigurationType.PHYSICAL,
    title = "POS 1 config"
)
private val POS_CONFIGURATION_2 = createPosConfiguration(
    macAddress = "XX:XX:XX:XX:XX",
    type = PosConfigurationType.ONLINE,
    title = "Online POS config"
)

// DAILY CLOSINGS
private val DAILY_CLOSING_1 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "2024000001",
    ticketsCount = 10,
    productsCount = 5,
    closedAt = NOW.minusDays(10),
    state = DailyClosingState.CLOSED
)
private val DAILY_CLOSING_3 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_1.id,
    receiptNumber = "2024000003",
    ticketsCount = 1,
    productsCount = 1,
    cancelledTicketsCount = 1,
    cancelledProductsCount = 1,
    closedAt = null,
    state = DailyClosingState.OPEN
)
private val DAILY_CLOSING_4 = createDailyClosing(
    posConfigurationId = POS_CONFIGURATION_2.id,
    receiptNumber = "2024000004",
    ticketsCount = 1,
    productsCount = 1,
    cancelledTicketsCount = 1,
    cancelledProductsCount = 1,
    closedAt = null,
    state = DailyClosingState.OPEN
)

// DAILY CLOSING MOVEMENTS
private val OTHER_DAILY_CLOSING_MOVEMENT = createDailyClosingMovement(
    dailyClosingId = DAILY_CLOSING_3.id,
    title = "Other movement",
    itemSubtype = DailyClosingMovementItemSubtype.OTHER,
    variableSymbol = "123456",
    receiptNumber = "DP00000001",
    otherReceiptNumber = "EXT0000001",
    amount = 25.5.toBigDecimal()
)
private val TICKET_SALES_DAILY_CLOSING_MOVEMENT = createDailyClosingMovement(
    title = "Ticket sales movement",
    dailyClosingId = DAILY_CLOSING_3.id,
    itemSubtype = DailyClosingMovementItemSubtype.TICKET_SALES,
    amount = 15.0.toBigDecimal()
)

private val DISTRIBUTOR = createDistributor()
private val AUDITORIUM = createAuditorium()
private val AUDITORIUM_LAYOUT = createAuditoriumLayout(auditoriumId = AUDITORIUM.id)
private val SEAT_1 = createSeat(originalId = 1, auditoriumLayoutId = AUDITORIUM_LAYOUT.id, auditoriumId = AUDITORIUM.id)
private val SEAT_2 = createSeat(originalId = 2, auditoriumLayoutId = AUDITORIUM_LAYOUT.id, auditoriumId = AUDITORIUM.id)
private val MOVIE = createMovie(distributorId = DISTRIBUTOR.id)
private val PRICE_CATEGORY = createPriceCategory()
private val SCREENING = createScreening(
    auditoriumId = AUDITORIUM.id,
    movieId = MOVIE.id,
    priceCategoryId = PRICE_CATEGORY.id,
    auditoriumLayoutId = AUDITORIUM_LAYOUT.id
)
private val RESERVATION_1 = createReservation(
    screeningId = SCREENING.id,
    seatId = SEAT_1.id
)
private val TICKET_PRICE_1 = createTicketPrice(
    screeningId = SCREENING.id,
    seatId = SEAT_1.id,
    totalPrice = 12.toBigDecimal(),
    serviceFeeGeneral = 5.toBigDecimal(),
    auditoriumServiceFee = 3.toBigDecimal(),
    seatServiceFee = 1.toBigDecimal()
)
private val RESERVATION_2 = createReservation(
    screeningId = SCREENING.id,
    seatId = SEAT_2.id
)
private val TICKET_PRICE_2 = createTicketPrice(
    screeningId = SCREENING.id,
    seatId = SEAT_2.id,
    totalPrice = 6.toBigDecimal(),
    serviceFeeGeneral = 2.5.toBigDecimal(),
    auditoriumServiceFee = 1.5.toBigDecimal(),
    seatServiceFee = 0.5.toBigDecimal()
)
private val TICKET_1 = createTicket(
    screeningId = SCREENING.id,
    reservationId = RESERVATION_1.id,
    ticketPriceId = TICKET_PRICE_1.id
)
private val TICKET_2 = createTicket(
    screeningId = SCREENING.id,
    reservationId = RESERVATION_2.id,
    ticketPriceId = TICKET_PRICE_2.id
)

// BASKETS
private val BASKET_1 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASH,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)
private val BASKET_2 = createBasket(
    state = BasketState.PAID,
    paymentType = PaymentType.CASHLESS,
    paymentPosConfigurationId = POS_CONFIGURATION_1.id
)

// BASKET ITEMS
private val BASKET_ITEM_TICKET_CASH = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.TICKET,
    price = 12.toBigDecimal(),
    ticketId = TICKET_1.id
)
private val BASKET_ITEM_TICKET_CASHLESS = copyBasketItem(BASKET_ITEM_TICKET_CASH) {
    it.basketId = BASKET_2.id
    it.price = 6.toBigDecimal()
    it.ticketId = TICKET_2.id
}
private val BASKET_ITEM_TICKET_CANCELLED_CASH = copyBasketItem(BASKET_ITEM_TICKET_CASH) {
    it.price = 8.toBigDecimal()
    it.cancelledBasketItemId = it.id
}
private val BASKET_ITEM_TICKET_CANCELLED_CASHLESS = copyBasketItem(BASKET_ITEM_TICKET_CASHLESS) {
    it.price = 4.toBigDecimal()
    it.cancelledBasketItemId = it.id
}
private val BASKET_ITEM_PRODUCT_CASH = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.PRODUCT,
    price = 23.toBigDecimal()
)
private val BASKET_ITEM_PRODUCT_CASHLESS = createBasketItem(
    basketId = BASKET_2.id,
    type = BasketItemType.PRODUCT,
    price = 25.toBigDecimal()
)
private val BASKET_ITEM_PRODUCT_CANCELLED_CASH = copyBasketItem(BASKET_ITEM_PRODUCT_CASH) {
    it.price = 13.toBigDecimal()
    it.cancelledBasketItemId = it.id
}
private val BASKET_ITEM_PRODUCT_CANCELLED_CASHLESS = copyBasketItem(BASKET_ITEM_PRODUCT_CASHLESS) {
    it.price = 15.toBigDecimal()
    it.cancelledBasketItemId = it.id
}
private val BASKET_ITEM_PRODUCT_DISCOUNT_CASH = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-5.5).toBigDecimal()
)
private val BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS = createBasketItem(
    basketId = BASKET_2.id,
    type = BasketItemType.PRODUCT_DISCOUNT,
    price = (-2.25).toBigDecimal()
)
private val BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASH =
    copyBasketItem(BASKET_ITEM_PRODUCT_DISCOUNT_CASH) {
        it.price = (-2.5).toBigDecimal()
        it.cancelledBasketItemId = it.id
    }
private val BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASHLESS =
    copyBasketItem(BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS) {
        it.price = (-1.5).toBigDecimal()
        it.cancelledBasketItemId = it.id
    }
private val BASKET_ITEM_PRODUCT_CASH_DELETED = createBasketItem(
    basketId = BASKET_1.id,
    type = BasketItemType.PRODUCT,
    price = 100.toBigDecimal()
) { it.markDeleted() }

private val ALL_BASKET_ITEMS = listOf(
    BASKET_ITEM_TICKET_CASH,
    BASKET_ITEM_TICKET_CASHLESS,
    BASKET_ITEM_TICKET_CANCELLED_CASH,
    BASKET_ITEM_TICKET_CANCELLED_CASHLESS,
    BASKET_ITEM_PRODUCT_CASH,
    BASKET_ITEM_PRODUCT_CASHLESS,
    BASKET_ITEM_PRODUCT_CANCELLED_CASH,
    BASKET_ITEM_PRODUCT_CANCELLED_CASHLESS,
    BASKET_ITEM_PRODUCT_DISCOUNT_CASH,
    BASKET_ITEM_PRODUCT_DISCOUNT_CASHLESS,
    BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASH,
    BASKET_ITEM_PRODUCT_DISCOUNT_CANCELLED_CASHLESS,
    BASKET_ITEM_PRODUCT_CASH_DELETED
)
