package com.cleevio.cinemax.api.module.stockmovement.controller

import com.cleevio.cinemax.api.ControllerTest
import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.STANDARD_TAX_RATE
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.util.jsonContent
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.AdminSearchStockMovementsResponse
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.CreateInputStockMovementsRequest
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.ProductComponentCategoryResponse
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.StockMovementProductComponentResponse
import com.cleevio.cinemax.api.module.stockmovement.controller.dto.StockMovementSupplierResponse
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateInputStockMovementsCommand
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateOutputStockMovementCommand
import com.cleevio.cinemax.api.module.stockmovement.service.command.UpdateInputStockMovementCommand
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminExportStockMovementsQuery
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminSearchStockMovementsFilter
import com.cleevio.cinemax.api.module.stockmovement.service.query.AdminSearchStockMovementsQuery
import com.cleevio.cinemax.api.truncatedAndFormatted
import com.cleevio.cinemax.api.util.mockAccessToken
import com.cleevio.cinemax.api.util.toUUID
import com.cleevio.cinemax.psql.tables.StockMovementColumnNames
import io.mockk.Runs
import io.mockk.every
import io.mockk.just
import io.mockk.verify
import io.mockk.verifySequence
import org.hamcrest.CoreMatchers
import org.junit.jupiter.api.Test
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest
import org.springframework.data.domain.PageImpl
import org.springframework.data.domain.PageRequest
import org.springframework.data.domain.Pageable
import org.springframework.data.domain.Sort
import org.springframework.http.HttpHeaders
import org.springframework.http.MediaType
import org.springframework.test.web.servlet.MockMvc
import org.springframework.test.web.servlet.post
import org.springframework.test.web.servlet.put
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.time.LocalDateTime

@WebMvcTest(AdminStockMovementController::class)
class AdminStockMovementControllerTest @Autowired constructor(
    private val mvc: MockMvc,
) : ControllerTest() {

    @Test
    fun `test createInputStockMovements, should serialize and deserialize correctly`() {
        val supplierId = 1.toUUID()
        val receiptNumber = "8904683643"
        val recordedAt = LocalDateTime.of(2023, 8, 15, 16, 30, 0)

        val productComponent1 = CreateInputStockMovementsRequest.CreateProductComponentRequest(
            productComponentId = 2.toUUID(),
            quantity = 2.5.toBigDecimal(),
            note = "Naskladneni popcornu"
        )
        val productComponent2 = CreateInputStockMovementsRequest.CreateProductComponentRequest(
            productComponentId = 3.toUUID(),
            quantity = 158.toBigDecimal(),
            note = null
        )

        every { stockMovementService.createInputStockMovements(any()) } just Runs

        mvc.post("$STOCK_MOVEMENT_BASE_PATH/input") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "supplierId": "$supplierId",
                  "receiptNumber": "$receiptNumber",
                  "recordedAt": "2023-08-15T14:30:00Z",
                  "productComponents": [
                    {
                      "productComponentId": "${productComponent1.productComponentId}",
                      "quantity": 2.5,
                      "note": "Naskladneni popcornu"
                    },
                    {
                      "productComponentId": "${productComponent2.productComponentId}",
                      "quantity": 158
                    }
                  ]
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            stockMovementService.createInputStockMovements(
                CreateInputStockMovementsCommand(
                    supplierId = supplierId,
                    receiptNumber = receiptNumber,
                    recordedAt = recordedAt,
                    productComponents = listOf(
                        CreateInputStockMovementsCommand.CreateInputStockMovementProductComponentCommand(
                            productComponentId = productComponent1.productComponentId,
                            quantity = productComponent1.quantity,
                            note = productComponent1.note
                        ),
                        CreateInputStockMovementsCommand.CreateInputStockMovementProductComponentCommand(
                            productComponentId = productComponent2.productComponentId,
                            quantity = productComponent2.quantity,
                            note = productComponent2.note
                        )
                    )
                )
            )
        }
    }

    @Test
    fun `test updateInputStockMovements, should serialize and deserialize correctly`() {
        val stockMovementId = 1.toUUID()
        val supplierId = 2.toUUID()
        val receiptNumber = "8904683643"
        val recordedAt = LocalDateTime.of(2023, 8, 15, 16, 30, 0)
        val note = "Presun tovarov do BB"

        every { stockMovementService.updateInputStockMovement(any()) } just Runs

        mvc.put("$STOCK_MOVEMENT_BASE_PATH/input/$stockMovementId") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "supplierId": "$supplierId",
                  "receiptNumber": "$receiptNumber",
                  "recordedAt": "2023-08-15T14:30:00Z",
                  "note": "Presun tovarov do BB"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verifySequence {
            stockMovementService.updateInputStockMovement(
                UpdateInputStockMovementCommand(
                    stockMovementId = stockMovementId,
                    supplierId = supplierId,
                    receiptNumber = receiptNumber,
                    recordedAt = recordedAt,
                    note = note
                )
            )
        }
    }

    @Test
    fun `test searchStockMovements - should serialize and deserialize correctly`() {
        val category = ProductComponentCategoryResponse(
            id = 1.toUUID(),
            title = "Beverages",
            taxRate = STANDARD_TAX_RATE
        )
        val supplier = StockMovementSupplierResponse(
            id = 2.toUUID(),
            title = "Best Supplier"
        )
        val productComponent = StockMovementProductComponentResponse(
            id = 3.toUUID(),
            title = "Coca Cola",
            originalCode = "01005",
            unit = ProductComponentUnit.L,
            category = category
        )
        val stockMovement = AdminSearchStockMovementsResponse(
            id = 4.toUUID(),
            type = StockMovementType.GOODS_RECEIPT,
            quantity = BigDecimal("10"),
            price = BigDecimal("5.99"),
            receiptNumber = "12345",
            note = "Sample stock movement",
            supplier = supplier,
            productComponent = productComponent,
            recordedAt = LocalDateTime.parse("2023-10-31T12:34:56"),
            createdAt = LocalDateTime.parse("2023-10-31T12:34:56"),
            updatedAt = LocalDateTime.parse("2023-11-01T12:34:56")
        )
        val recordedAtFrom = LocalDateTime.parse("2023-10-30T00:00:00")
        val recordedAtTo = LocalDateTime.parse("2023-11-01T00:00:00")

        every { adminSearchStockMovementsQueryService(any()) } returns PageImpl(
            listOf(stockMovement)
        )

        mvc.post(STOCK_MOVEMENT_SEARCH_PATH) {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
            {
              "types": ["GOODS_RECEIPT"],
              "recordedAtFrom": "${recordedAtFrom.truncatedAndFormatted()}",
              "recordedAtTo": "${recordedAtTo.truncatedAndFormatted()}",
              "productComponentOriginalCode": "010",
              "productComponentTitle": "Coca Cola",
              "supplierIds": ["${supplier.id}"],
              "note": "Sample stock movement",
              "receiptNumber": "12345"
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            jsonContent(
                """
                {
                  "content": [
                    {
                      "id": "${stockMovement.id}",
                      "type": "GOODS_RECEIPT",
                      "quantity": 10,
                      "price": 5.99,
                      "receiptNumber": "${stockMovement.receiptNumber}",
                      "note": "${stockMovement.note}",
                      "supplier": {
                        "id": "${supplier.id}",
                        "title": "${supplier.title}"
                      },
                      "productComponent": {
                        "id": "${productComponent.id}",
                        "title": "${productComponent.title}",
                        "originalCode": "${productComponent.originalCode}",
                        "unit": "${productComponent.unit}",
                        "category": {
                          "id": "${category.id}",
                          "title": "${category.title}",
                          "taxRate": ${category.taxRate}
                        }
                      },
                      "recordedAt": "${stockMovement.recordedAt.truncatedAndFormatted()}",
                      "createdAt": "${stockMovement.createdAt.truncatedAndFormatted()}",
                      "updatedAt": "${stockMovement.updatedAt.truncatedAndFormatted()}"
                    }
                  ],
                  "totalElements": 1,
                  "totalPages": 1
                }
                """.trimIndent()
            )
        }

        verify {
            adminSearchStockMovementsQueryService(
                AdminSearchStockMovementsQuery(
                    filter = AdminSearchStockMovementsFilter(
                        types = setOf(StockMovementType.GOODS_RECEIPT),
                        recordedAtFrom = recordedAtFrom,
                        recordedAtTo = recordedAtTo,
                        productComponentOriginalCode = "010",
                        productComponentTitle = "Coca Cola",
                        supplierIds = setOf(supplier.id),
                        note = "Sample stock movement",
                        receiptNumber = "12345"
                    ),
                    pageable = PageRequest.of(0, 10, Sort.by(StockMovementColumnNames.RECORDED_AT).descending())
                )
            )
        }
    }

    @Test
    fun `test createOutputStockMovements, should serialize and deserialize correctly`() {
        val productComponentId = 2.toUUID()
        val recordedAt = LocalDateTime.of(2023, 8, 15, 16, 30, 0)
        val quantity = BigDecimal("5.0")
        val note = "Prosly popcorn"

        every { stockMovementService.createOutputStockMovement(any()) } just Runs

        mvc.post("$STOCK_MOVEMENT_BASE_PATH/output") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_JSON)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
                {
                  "type": "WRITE_OFF_WARRANTY",
                  "productComponentId": "$productComponentId",
                  "recordedAt": "${recordedAt.truncatedAndFormatted()}",
                  "quantity": $quantity,
                  "note": "$note"
                }
            """.trimIndent()
        }.andExpect {
            status { isNoContent() }
        }

        verify {
            stockMovementService.createOutputStockMovement(
                CreateOutputStockMovementCommand(
                    type = StockMovementType.WRITE_OFF_WARRANTY,
                    productComponentId = productComponentId,
                    recordedAt = recordedAt,
                    quantity = quantity,
                    note = note
                )
            )
        }
    }

    @Test
    fun `test exportStockMovements - should call service and return excel file`() {
        val filter = AdminSearchStockMovementsFilter(
            recordedAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            recordedAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
            productComponentOriginalCode = "02",
            productComponentTitle = "Kukurica"
        )

        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { stockMovementExportService.exportStockMovements(any()) } returns exportResult

        mvc.post("$STOCK_MOVEMENTS_BASE_PATH/export/${ExportFormat.XLSX}") {
            header(HttpHeaders.ACCEPT, ApiVersion.VERSION_1_XLSX)
            header(HttpHeaders.AUTHORIZATION, mockAccessToken(EmployeeRole.MANAGER))
            contentType = MediaType.APPLICATION_JSON
            content = """
            {
              "recordedAtFrom": "2023-08-15T14:30:00Z",
              "recordedAtTo": "2023-08-20T18:45:00Z",
              "productComponentOriginalCode": "02",
              "productComponentTitle": "Kukurica"
            }
            """.trimIndent()
        }.andExpect {
            status { isOk() }
            header {
                string(HttpHeaders.CONTENT_TYPE, ApiVersion.VERSION_1_XLSX)
                string(HttpHeaders.CONTENT_DISPOSITION, CoreMatchers.containsString("attachment"))
                longValue(HttpHeaders.CONTENT_LENGTH, byteArray.size.toLong())
            }
        }

        verify {
            stockMovementExportService.exportStockMovements(
                AdminExportStockMovementsQuery(
                    pageable = Pageable.unpaged(Sort.by(StockMovementColumnNames.RECORDED_AT).descending()),
                    filter = filter,
                    exportFormat = ExportFormat.XLSX,
                    username = "anonymous"
                )
            )
        }
    }
}

private const val STOCK_MOVEMENT_BASE_PATH = "/manager-app/stock-movements"
private const val STOCK_MOVEMENT_SEARCH_PATH = "$STOCK_MOVEMENT_BASE_PATH/search"
private const val STOCK_MOVEMENTS_BASE_PATH = "/manager-app/stock-movements"
