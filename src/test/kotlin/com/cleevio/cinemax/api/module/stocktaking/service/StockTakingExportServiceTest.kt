package com.cleevio.cinemax.api.module.stocktaking.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.stocktaking.service.model.StockTakingExportRecordModel
import com.cleevio.cinemax.api.module.stocktaking.service.query.AdminExportStockTakingsFilter
import com.cleevio.cinemax.api.module.stocktaking.service.query.AdminExportStockTakingsQuery
import io.mockk.every
import io.mockk.mockk
import io.mockk.verify
import io.mockk.verifySequence
import org.junit.jupiter.api.assertThrows
import org.springframework.data.domain.Pageable
import java.io.ByteArrayInputStream
import java.time.LocalDateTime
import kotlin.test.Test
import kotlin.test.assertEquals

class StockTakingExportServiceTest {

    private val adminExportStockTakingsQueryService = mockk<AdminExportStockTakingsQueryService>()
    private val stockTakingXlsxExportResultMapper = mockk<StockTakingXlsxExportResultMapper>()
    private val underTest = StockTakingExportService(
        adminExportStockTakingsQueryService = adminExportStockTakingsQueryService,
        stockTakingXlsxExportResultMapper = stockTakingXlsxExportResultMapper
    )

    @Test
    fun `test exportStockTakings - valid query with XLSX format - should call related service and mapper`() {
        val username = "username"
        val filter = AdminExportStockTakingsFilter(
            createdAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            createdAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
            productComponentOriginalCode = "02",
            productComponentTitle = "Kukuric",
            nonZeroQuantityDifferenceOnly = true
        )
        val query = AdminExportStockTakingsQuery(
            pageable = Pageable.unpaged(),
            filter = filter,
            exportFormat = ExportFormat.XLSX,
            username = username
        )

        val exportData = listOf(
            StockTakingExportRecordModel(
                productComponentOriginalCode = "02005",
                productComponentTitle = "Kukurica extra",
                stockQuantity = 120.25.toBigDecimal(),
                stockQuantityActual = 110.25.toBigDecimal(),
                stockQuantityDifference = (-10).toBigDecimal(),
                unit = ProductComponentUnit.KG,
                purchasePrice = 10.5.toBigDecimal(),
                purchasePriceDifference = (-105).toBigDecimal()
            )
        )
        val byteArray = ByteArray(1024)
        val inputStream = ByteArrayInputStream(byteArray)
        val exportResult = ExportResultModel(inputStream, byteArray.size.toLong())

        every { adminExportStockTakingsQueryService(query) } returns exportData
        every {
            stockTakingXlsxExportResultMapper.mapToExportResultModel(
                any(),
                any(),
                any(),
                any()
            )
        } returns exportResult

        assertEquals(exportResult, underTest.exportStockTakings(query))

        verifySequence {
            adminExportStockTakingsQueryService(query)
            stockTakingXlsxExportResultMapper.mapToExportResultModel(
                data = exportData,
                username = username,
                createdAtFrom = filter.createdAtFrom,
                createdAtTo = filter.createdAtTo
            )
        }
    }

    @Test
    fun `test exportStockTakings - valid query with XML format - should throw`() {
        val username = "username"
        val filter = AdminExportStockTakingsFilter(
            createdAtFrom = LocalDateTime.of(2023, 8, 15, 16, 30, 0),
            createdAtTo = LocalDateTime.of(2023, 8, 20, 20, 45, 0),
            productComponentOriginalCode = "02",
            productComponentTitle = "Kukuric",
            nonZeroQuantityDifferenceOnly = true

        )
        val query = AdminExportStockTakingsQuery(
            pageable = Pageable.unpaged(),
            filter = filter,
            exportFormat = ExportFormat.XML,
            username = username
        )

        assertThrows<UnsupportedOperationException> { underTest.exportStockTakings(query) }
        verify(exactly = 0) { adminExportStockTakingsQueryService(any()) }
        verify(exactly = 0) { stockTakingXlsxExportResultMapper.mapToExportResultModel(any(), any(), any(), any()) }
    }
}
