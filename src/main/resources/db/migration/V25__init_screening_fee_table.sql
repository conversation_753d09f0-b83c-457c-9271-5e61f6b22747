CREATE TABLE screening_fee
(
   id                    UUID PRIMARY KEY,
   original_screening_id INT NOT NULL,
   screening_id          UUID NOT NULL,
   surcharge_vip         NUMERIC,
   surcharge_premium     NUMERIC,
   surcharge_imax        NUMERIC,
   surcharge_ultra_x     NUMERIC,
   service_fee_vip       NUMERIC,
   service_fee_premium   NUMERIC,
   service_fee_imax      NUMERIC,
   service_fee_ultra_x   NUMERIC,
   service_fee_d_box     NUMERIC,
   service_fee_general   NUMERIC,
   created_at            TIMESTAMP NOT NULL,
   updated_at            TIMESTAMP NOT NULL
);

CREATE UNIQUE INDEX "73b9c919f1e8492fac3f_ui" ON screening_fee (original_screening_id);
ALTER TABLE screening_fee ADD CONSTRAINT "7d170da519a24b2383af_fk" FOREIGN KEY (screening_id) REFERENCES screening(id);
