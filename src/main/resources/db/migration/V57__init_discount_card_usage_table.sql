CREATE TABLE discount_card_usage
(
     id                   UUID PRIMARY KEY,
     discount_card_id     UUID NOT NULL,
     screening_id         UUID,
     basket_id            UUID NOT NULL,
     pos_configuration_id UUID NOT NULL,
     created_at           TIMESTAMP NOT NULL,
     deleted_at           TIMESTAMP
  );

ALTER TABLE discount_card_usage ADD CONSTRAINT "3d4e87a93d0f41e3ae09_fk" FOREIGN KEY (discount_card_id) REFERENCES discount_card(id);
ALTER TABLE discount_card_usage ADD CONSTRAINT "874f9923266548a0b0a0_fk" FOREIGN KEY (screening_id) REFERENCES screening(id);
ALTER TABLE discount_card_usage ADD CONSTRAINT "32c2e6776d7348bea5c6_fk" FOREIGN KEY (basket_id) REFERENCES basket(id);
ALTER TABLE discount_card_usage ADD CONSTRAINT "aafd6c4f40764556895b_fk" FOREIGN KEY (pos_configuration_id) REFERENCES pos_configuration(id);

CREATE INDEX "951f7567300f491393fe_ix" ON discount_card_usage (discount_card_id, deleted_at);
