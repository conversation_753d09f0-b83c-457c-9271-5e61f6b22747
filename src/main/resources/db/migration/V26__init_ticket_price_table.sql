CREATE TABLE ticket_price
(
   id                          UUID PRIMARY KEY,
   screening_id                UUID NOT NULL,
   seat_id                     UUID NOT NULL,
   state                       TEXT NOT NULL,
   basic_price                  NUMERIC NOT NULL,
   basic_price_item_number     TEXT NOT NULL,
   seat_surcharge              NUMERIC,
   seat_surcharge_type         TEXT,
   auditorium_surcharge        NUMERIC,
   auditorium_surcharge_type   TEXT,
   seat_service_fee            NUMERIC,
   seat_service_fee_type       TEXT,
   auditorium_service_fee      NUMERIC,
   auditorium_service_fee_type TEXT,
   service_fee_general         NUMERIC,
   total_price                 NUMERIC NOT NULL,
   created_at                  TIMESTAMP NOT NULL,
   updated_at                  TIMESTAMP NOT NULL,
   expires_at                  TIMESTAMP NOT NULL
);

CREATE INDEX "b81f0a601088492ba6f2_ix" ON ticket_price (screening_id, seat_id);
