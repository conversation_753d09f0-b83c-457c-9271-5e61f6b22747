DELETE FROM branch;
ALTER TABLE branch ADD COLUMN auditorium_original_code_prefix TEXT NOT NULL;
CREATE UNIQUE INDEX "387cadc8b99a46ca9ad4_ui" ON branch (auditorium_original_code_prefix);

ALTER TABLE auditorium ADD COLUMN branch_id UUID;
ALTER TABLE auditorium ADD CONSTRAINT "fa9df5dcc26f47f98bb2_fk"
    FOREIGN KEY (branch_id) REFERENCES branch(id);
CREATE INDEX "46857973c2b24390a5da_ix" ON auditorium (branch_id);
