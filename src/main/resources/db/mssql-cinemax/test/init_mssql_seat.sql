-----------------------
--- init test table ---
-----------------------
SET ANSI_NULLS ON
;
SET QUOTED_IDENTIFIER ON
;
CREATE TABLE [dbo].[rmustr](
    [rmustrID] [int] IDENTITY(1,1) NOT NULL,
    [rupravaID] [int] NOT NULL,
    [rodbID] [int] NOT NULL,
    [csalu] [char](6) NOT NULL,
    [sekce] [char](3) NOT NULL,
    [typ] [char](1) NOT NULL,
    [jmeno] [char](10) NOT NULL,
    [popis] [varchar](50) NOT NULL,
    [vlevo] [smallint] NOT NULL,
    [vrch] [smallint] NOT NULL,
    [sirka] [smallint] NOT NULL,
    [vyska] [smallint] NOT NULL,
    [rada] [smallint] NOT NULL,
    [sloup] [smallint] NOT NULL,
    [oznr] [char](4) NOT NULL,
    [podsekce] [char](15) NOT NULL,
    [umisteni] [char](15) NOT NULL,
    [oznk] [char](5) NOT NULL,
    [oznd] [char](5) NOT NULL,
    [oznl] [char](2) NOT NULL,
    [fontV] [tinyint] NOT NULL,
    [tucne] [bit] NOT NULL,
    [cursiva] [bit] NOT NULL,
    [barvav] [int] NOT NULL,
    [barvas] [int] NOT NULL,
    [cena] [tinyint] NOT NULL,
    [delegace] [tinyint] NOT NULL,
    [zobrazit] [bit] NOT NULL,
    [tol] [smallint] NOT NULL,
    [ceny] [varchar](51) NOT NULL,
    [zuziv] [char](10) NOT NULL,
    [zcas] [smalldatetime] NULL,
    [levadvoj] [bit] NOT NULL,
    [pravadvoj] [bit] NOT NULL,
    [coordsLeft] [int] NULL,
    [coordsTop] [int] NULL,
    [breakPosition] [varchar](255) NULL,
    PRIMARY KEY NONCLUSTERED
(
[rmustrID] ASC
)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]
    ) ON [PRIMARY]
;
SET ANSI_PADDING ON
;

ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [rupravaID]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [rodbID]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('') FOR [sekce]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('S') FOR [typ]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('') FOR [jmeno]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((9)) FOR [fontV]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((1)) FOR [tucne]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [cursiva]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [barvav]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((12632256)) FOR [barvas]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((1)) FOR [cena]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((1)) FOR [delegace]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((1)) FOR [zobrazit]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [tol]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('') FOR [ceny]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ('') FOR [zuziv]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT (getdate()) FOR [zcas]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [levadvoj]
;
ALTER TABLE [dbo].[rmustr] ADD  DEFAULT ((0)) FOR [pravadvoj]
;

----------------------
--- init test data ---
----------------------

INSERT INTO rps_CinemaxE.dbo.rmustr (rupravaID, rodbID, csalu, sekce, typ, jmeno, popis, vlevo, vrch, sirka, vyska, rada, sloup, oznr, podsekce, umisteni, oznk, oznd, oznl, fontV, tucne, cursiva, barvav, barvas, cena, delegace, zobrazit, tol, ceny, zuziv, zcas, levadvoj, pravadvoj, coordsLeft, coordsTop, breakPosition) VALUES (2, 0, N'510000', N'01 ', N'S', N'S_010_003 ', N'19                                                ', 198, 367, 30, 30, 10, 3, N'10  ', N'               ', N'               ', N'     ', N'     ', N'  ', 9, 1, 0, 16512, 12632256, 1, 4, 1, 127, N'1,2,', N'adamko    ', N'2022-01-12 10:50:00', 1, 0, 85, 232, N'');
INSERT INTO rps_CinemaxE.dbo.rmustr (rupravaID, rodbID, csalu, sekce, typ, jmeno, popis, vlevo, vrch, sirka, vyska, rada, sloup, oznr, podsekce, umisteni, oznk, oznd, oznl, fontV, tucne, cursiva, barvav, barvas, cena, delegace, zobrazit, tol, ceny, zuziv, zcas, levadvoj, pravadvoj, coordsLeft, coordsTop, breakPosition) VALUES (4, 0, N'510000', N'01 ', N'S', N'S_005_002 ', N'2                                                 ', 320, 166, 29, 30, 5, 2, N'5   ', N'               ', N'               ', N'Premi', N'     ', N'  ', 9, 1, 0, 255, 12632256, 3, 0, 1, 54, N'1,2,', N'adamko    ', N'2022-02-23 16:48:00', 0, 0, 176, 136, N'');
INSERT INTO rps_CinemaxE.dbo.rmustr (rupravaID, rodbID, csalu, sekce, typ, jmeno, popis, vlevo, vrch, sirka, vyska, rada, sloup, oznr, podsekce, umisteni, oznk, oznd, oznl, fontV, tucne, cursiva, barvav, barvas, cena, delegace, zobrazit, tol, ceny, zuziv, zcas, levadvoj, pravadvoj, coordsLeft, coordsTop, breakPosition) VALUES (1, 0, N'510000', N'01 ', N'S', N'S_002_009 ', N'Inv.                                              ', 700, 62, 21, 23, 2, 9, N'3   ', N'               ', N'               ', N'     ', N'     ', N'  ', 9, 1, 0, 16512, 12632256, 1, 1, 1, 0, N'1,2,', N'adamko    ', N'2022-02-23 16:49:00', 0, 0, 135, 74, N'');
-- seat with invalid coords
INSERT INTO rps_CinemaxE.dbo.rmustr (rupravaID, rodbID, csalu, sekce, typ, jmeno, popis, vlevo, vrch, sirka, vyska, rada, sloup, oznr, podsekce, umisteni, oznk, oznd, oznl, fontV, tucne, cursiva, barvav, barvas, cena, delegace, zobrazit, tol, ceny, zuziv, zcas, levadvoj, pravadvoj, coordsLeft, coordsTop, breakPosition) VALUES (2, 0, N'510000', N'01 ', N'S', N'S_010_003 ', N'19                                                ', 198, 367, 30, 30, 10, 3, N'10  ', N'               ', N'               ', N'     ', N'     ', N'  ', 9, 1, 0, 16512, 12632256, 1, 1, 1, 127, N'1,2,', N'adamko    ', N'2022-01-12 10:50:00', 1, 0, null, 232, N'');
-- seat with invalid double seat data
INSERT INTO rps_CinemaxE.dbo.rmustr (rupravaID, rodbID, csalu, sekce, typ, jmeno, popis, vlevo, vrch, sirka, vyska, rada, sloup, oznr, podsekce, umisteni, oznk, oznd, oznl, fontV, tucne, cursiva, barvav, barvas, cena, delegace, zobrazit, tol, ceny, zuziv, zcas, levadvoj, pravadvoj, coordsLeft, coordsTop, breakPosition) VALUES (2, 0, N'510000', N'01 ', N'S', N'S_010_003 ', N'19                                                ', 198, 367, 30, 30, 10, 3, N'10  ', N'               ', N'               ', N'     ', N'     ', N'  ', 9, 1, 0, 16512, 12632256, 1, 1, 1, 127, N'1,2,', N'adamko    ', N'2022-01-12 10:50:00', 1, 1, 85, 232, N'');
-- seat with invalid type
INSERT INTO rps_CinemaxE.dbo.rmustr (rupravaID, rodbID, csalu, sekce, typ, jmeno, popis, vlevo, vrch, sirka, vyska, rada, sloup, oznr, podsekce, umisteni, oznk, oznd, oznl, fontV, tucne, cursiva, barvav, barvas, cena, delegace, zobrazit, tol, ceny, zuziv, zcas, levadvoj, pravadvoj, coordsLeft, coordsTop, breakPosition) VALUES (2, 0, N'510000', N'01 ', N'R', N'S_010_003 ', N'19                                                ', 198, 367, 30, 30, 10, 3, N'10  ', N'               ', N'               ', N'     ', N'     ', N'  ', 9, 1, 0, 16512, 12632256, 1, 1, 1, 127, N'1,2,', N'adamko    ', N'2022-01-12 10:50:00', 1, 0, 85, 232, N'');
