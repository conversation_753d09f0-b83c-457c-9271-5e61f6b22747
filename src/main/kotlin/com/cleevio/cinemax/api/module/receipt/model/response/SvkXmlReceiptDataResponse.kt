package com.cleevio.cinemax.api.module.receipt.model.response

import com.fasterxml.jackson.dataformat.xml.annotation.JacksonXmlProperty

data class SvkXmlReceiptDataResponse(

    @field:JacksonXmlProperty(localName = "Success", isAttribute = true)
    val success: <PERSON><PERSON><PERSON>,

    @field:JacksonXmlProperty(localName = "ReceiptNumber", isAttribute = true)
    val receiptNumber: String,

    @field:JacksonXmlProperty(localName = "EkasaErrorCode", isAttribute = true)
    val errorCode: Int,

    @field:JacksonXmlProperty(localName = "Id", isAttribute = true)
    val id: String,

    @field:JacksonXmlProperty(localName = "OKP", isAttribute = true)
    val okp: String,
)
