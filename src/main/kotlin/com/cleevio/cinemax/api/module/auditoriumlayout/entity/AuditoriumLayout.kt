package com.cleevio.cinemax.api.module.auditoriumlayout.entity

import com.cleevio.cinemax.api.common.entity.DeletableEntity
import jakarta.persistence.Column
import java.util.UUID
import jakarta.persistence.Entity as JpaEntity

@JpaEntity
class AuditoriumLayout(
    id: UUID = UUID.randomUUID(),

    @Column(name = "original_id")
    var originalId: Int?,

    @Column(name = "auditorium_id", nullable = false)
    var auditoriumId: UUID,

    @Column(name = "code", nullable = false)
    var code: String,

    @Column(name = "title", nullable = false)
    var title: String,

) : DeletableEntity(id)
