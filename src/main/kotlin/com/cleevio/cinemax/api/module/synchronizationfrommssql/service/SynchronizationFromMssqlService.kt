package com.cleevio.cinemax.api.module.synchronizationfrommssql.service

import com.cleevio.cinemax.api.module.synchronizationfrommssql.entity.SynchronizationFromMssql
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.CreateOrUpdateSynchronizationFromMssqlCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.command.UpdateLastHeartbeatCommand
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.time.LocalDateTime

@Service
@Validated
class SynchronizationFromMssqlService(
    private val synchronizationFromMssqlRepository: SynchronizationFromMssqlRepository,
) {
    @Transactional
    fun createOrUpdate(@Valid command: CreateOrUpdateSynchronizationFromMssqlCommand): SynchronizationFromMssql {
        synchronizationFromMssqlRepository.findByType(command.type).also {
            return if (it.isEmpty) {
                synchronizationFromMssqlRepository.save(mapToNewSynchronizationFromMssql(command))
            } else {
                synchronizationFromMssqlRepository.save(mapToExistingSynchronizationFromMssql(it.get(), command))
            }
        }
    }

    @Transactional
    fun updateLastHeartbeat(@Valid command: UpdateLastHeartbeatCommand) {
        synchronizationFromMssqlRepository.findByType(command.type).also {
            if (it.isPresent) {
                synchronizationFromMssqlRepository.save(
                    it.get().apply {
                        lastHeartbeat = LocalDateTime.now()
                    }
                )
            }
        }
    }

    private fun mapToNewSynchronizationFromMssql(
        command: CreateOrUpdateSynchronizationFromMssqlCommand,
    ) = SynchronizationFromMssql(
        type = command.type,
        lastSynchronization = command.lastSynchronization,
        lastHeartbeat = null
    )

    private fun mapToExistingSynchronizationFromMssql(
        synchronizationFromMssql: SynchronizationFromMssql,
        command: CreateOrUpdateSynchronizationFromMssqlCommand,
    ) = synchronizationFromMssql.apply {
        lastSynchronization = command.lastSynchronization
    }
}
