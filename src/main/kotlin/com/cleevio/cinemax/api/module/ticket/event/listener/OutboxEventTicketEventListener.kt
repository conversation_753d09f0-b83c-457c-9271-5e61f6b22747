package com.cleevio.cinemax.api.module.ticket.event.listener

import com.cleevio.cinemax.api.module.ticket.event.TicketSyncedToMssqlEvent
import com.cleevio.cinemax.api.module.ticket.service.TicketService
import com.cleevio.cinemax.api.module.ticket.service.command.UpdateTicketOriginalIdCommand
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.ticket.outbox-event.listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class OutboxEventTicketEventListener(
    private val ticketService: TicketService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT, fallbackExecution = true)
    fun listenToTicketSyncedToMssqlEvent(event: TicketSyncedToMssqlEvent) {
        ticketService.updateTicketOriginalId(
            UpdateTicketOriginalIdCommand(
                ticketId = event.ticketId,
                originalId = event.originalTicketId
            )
        )
    }
}
