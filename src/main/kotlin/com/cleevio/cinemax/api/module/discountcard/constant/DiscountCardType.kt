package com.cleevio.cinemax.api.module.discountcard.constant

import com.cleevio.cinemax.api.common.constant.ExportLanguage
import com.cleevio.cinemax.api.common.service.ExportableEnum

enum class DiscountCardType : ExportableEnum {
    CARD {
        override fun getLocalizedNames(): Map<ExportLanguage, String> {
            return mapOf(
                ExportLanguage.CZECH to "karta",
                ExportLanguage.SLOVAK to "karta",
                ExportLanguage.ROMANIAN to "card"
            )
        }
    },
    VOUCHER {
        override fun getLocalizedNames(): Map<ExportLanguage, String> {
            return mapOf(
                ExportLanguage.CZECH to "voucher",
                ExportLanguage.SLOVAK to "voucher",
                ExportLanguage.ROMANIAN to "voucher"
            )
        }
    },
}
