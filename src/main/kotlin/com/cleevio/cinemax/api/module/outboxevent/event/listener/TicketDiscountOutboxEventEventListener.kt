package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import com.cleevio.cinemax.api.module.ticketdiscount.event.TicketDiscountCreatedOrUpdatedEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.outbox-event.ticket-discount.listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class TicketDiscountOutboxEventEventListener(
    private val outboxEventService: OutboxEventService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToTicketDiscountCreatedOrUpdatedEvent(event: TicketDiscountCreatedOrUpdatedEvent) {
        outboxEventService.createOutboxEvent(
            CreateOutboxEventCommand(
                entityId = event.ticketDiscountId,
                type = OutboxEventType.TICKET_DISCOUNT_CREATED_OR_UPDATED
            )
        )
    }
}
