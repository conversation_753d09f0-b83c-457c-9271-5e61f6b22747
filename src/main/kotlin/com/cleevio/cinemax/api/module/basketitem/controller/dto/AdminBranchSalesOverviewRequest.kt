package com.cleevio.cinemax.api.module.basketitem.controller.dto

import com.cleevio.cinemax.api.module.basketitem.service.query.AdminBranchSalesOverviewQuery
import java.util.UUID

data class AdminBranchSalesOverviewRequest(
    val selectedYear: Int,
    val branchIds: Set<UUID>? = null,
) {
    fun toQuery() = AdminBranchSalesOverviewQuery(
        selectedYear = selectedYear,
        branchIds = branchIds
    )
}
