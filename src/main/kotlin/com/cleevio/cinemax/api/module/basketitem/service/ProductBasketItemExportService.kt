package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.module.basketitem.service.query.AdminExportProductBasketItemsQuery
import jakarta.validation.Valid
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class ProductBasketItemExportService(
    private val productBasketItemXlsxExportResultMapper: ProductBasketItemXlsxExportResultMapper,
    private val adminExportProductBasketItemsSummaryQueryService: AdminExportProductBasketItemsSummaryQueryService,
    private val adminExportProductBasketItemsQueryService: AdminExportProductBasketItemsQueryService,
) {

    fun exportProductBasketItems(@Valid query: AdminExportProductBasketItemsQuery): ExportResultModel =
        when (query.exportFormat) {
            ExportFormat.XLSX -> exportProductBasketItemsToXlsx(query)
            ExportFormat.XML -> throw UnsupportedOperationException("XML export is not supported.")
        }

    private fun exportProductBasketItemsToXlsx(query: AdminExportProductBasketItemsQuery): ExportResultModel =
        productBasketItemXlsxExportResultMapper.mapToExportResultModel(
            data = adminExportProductBasketItemsQueryService(query) to adminExportProductBasketItemsSummaryQueryService(query),
            username = query.username,
            basketPaidAtDateFrom = query.filter.basketPaidAtFrom?.toLocalDate(),
            basketPaidAtDateTo = query.filter.basketPaidAtTo?.toLocalDate()
        )
}
