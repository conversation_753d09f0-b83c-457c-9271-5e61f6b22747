package com.cleevio.cinemax.api.module.basketitem.service.command

import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import jakarta.validation.constraints.Positive
import java.util.Optional
import java.util.UUID

data class PatchBasketItemCommand(
    val basketId: UUID,
    val basketItemId: UUID,

    @field:Positive
    val quantity: Int? = null,
    val priceCategoryItemNumber: PriceCategoryItemNumber? = null,
    val primaryTicketDiscountId: Optional<UUID>? = null,
    val secondaryTicketDiscountId: Optional<UUID>? = null,
    val primaryTicketDiscountCardId: UUID? = null,
    val secondaryTicketDiscountCardId: UUID? = null,
    val patchDeletedBasketItems: Boolean = false,
) {

    fun isEmpty(): Boolean {
        return setOf(
            this.quantity,
            this.priceCategoryItemNumber,
            this.primaryTicketDiscountId,
            this.secondaryTicketDiscountId
        ).all { it == null }
    }
}
