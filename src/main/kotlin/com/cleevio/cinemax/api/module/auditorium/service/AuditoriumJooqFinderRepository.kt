package com.cleevio.cinemax.api.module.auditorium.service

import com.cleevio.cinemax.api.common.service.JooqFinderRepository
import com.cleevio.cinemax.api.module.auditorium.entity.Auditorium
import com.cleevio.cinemax.psql.tables.Auditorium.AUDITORIUM
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
class AuditoriumJooqFinderRepository(
    private val psqlDslContext: DSLContext,
) : JooqFinderRepository<Auditorium> {

    override fun findAll(): List<Auditorium> {
        return psqlDslContext
            .selectFrom(AUDITORIUM)
            .fetchInto(Auditorium::class.java)
    }

    fun findAllByIdIn(ids: Set<UUID>): List<Auditorium> {
        return psqlDslContext
            .selectFrom(AUDITORIUM)
            .where(AUDITORIUM.ID.`in`(ids))
            .fetchInto(Auditorium::class.java)
    }

    override fun findById(id: UUID): Auditorium? {
        return psqlDslContext
            .selectFrom(AUDITORIUM)
            .where(AUDITORIUM.ID.eq(id))
            .fetchOneInto(Auditorium::class.java)
    }

    fun findByOriginalId(originalId: Int): Auditorium? {
        return psqlDslContext
            .selectFrom(AUDITORIUM)
            .where(AUDITORIUM.ORIGINAL_ID.eq(originalId))
            .fetchOneInto(Auditorium::class.java)
    }

    fun findByOriginalCode(originalCode: Int): Auditorium? {
        return psqlDslContext
            .selectFrom(AUDITORIUM)
            .where(AUDITORIUM.ORIGINAL_CODE.eq(originalCode))
            .fetchOneInto(Auditorium::class.java)
    }
}
