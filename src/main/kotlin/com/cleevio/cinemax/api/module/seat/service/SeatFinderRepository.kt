package com.cleevio.cinemax.api.module.seat.service

import com.cleevio.cinemax.api.common.service.JooqFinderRepository
import com.cleevio.cinemax.api.module.seat.entity.Seat
import com.cleevio.cinemax.psql.tables.Seat.SEAT
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.impl.DSL.or
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
class SeatFinderRepository(
    private val psqlDslContext: DSLContext,
) : JooqFinderRepository<Seat> {

    override fun findAll(): List<Seat> {
        return psqlDslContext
            .selectFrom(SEAT)
            .fetchInto(Seat::class.java)
    }

    override fun findById(id: UUID): Seat? {
        return psqlDslContext
            .selectFrom(SEAT)
            .where(SEAT.ID.eq(id))
            .fetchOneInto(Seat::class.java)
    }

    fun findAllByIdIn(ids: Set<UUID>): List<Seat> {
        return psqlDslContext
            .selectFrom(SEAT)
            .where(SEAT.ID.`in`(ids))
            .fetchInto(Seat::class.java)
    }

    fun findByOriginalId(originalId: Int): Seat? {
        return psqlDslContext
            .selectFrom(SEAT)
            .where(SEAT.ORIGINAL_ID.eq(originalId))
            .fetchOneInto(Seat::class.java)
    }

    fun findByIdAndAuditoriumLayoutId(id: UUID, auditoriumLayoutId: UUID): Seat? {
        return psqlDslContext
            .selectFrom(SEAT)
            .where(SEAT.ID.eq(id).and(SEAT.AUDITORIUM_LAYOUT_ID.eq(auditoriumLayoutId)))
            .fetchOneInto(Seat::class.java)
    }

    fun findAllByAuditoriumIdAndAuditoriumLayoutIdIn(auditoriumIdToAuditoriumLayoutIdPairs: Set<Pair<UUID, UUID>>): List<Seat> {
        return psqlDslContext
            .selectFrom(SEAT)
            .where(buildAuditoriumLayoutOrCondition(auditoriumIdToAuditoriumLayoutIdPairs))
            .fetchInto(Seat::class.java)
    }

    private fun buildAuditoriumLayoutOrCondition(auditoriumIdToAuditoriumLayoutIdPairs: Set<Pair<UUID, UUID>>): Condition {
        var condition = or()
        auditoriumIdToAuditoriumLayoutIdPairs.forEach {
            condition = condition.or(SEAT.AUDITORIUM_ID.eq(it.first).and(SEAT.AUDITORIUM_LAYOUT_ID.eq(it.second)))
        }
        return condition
    }
}
