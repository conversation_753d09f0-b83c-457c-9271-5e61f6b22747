package com.cleevio.cinemax.api.module.stockmovement.controller.dto

import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateOutputStockMovementCommand
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class CreateOutputStockMovementRequest(
    val type: StockMovementType,
    val productComponentId: UUID,
    val recordedAt: LocalDateTime,
    val quantity: BigDecimal,
    val note: String? = null,
) {
    fun toCommand() = CreateOutputStockMovementCommand(
        type = type,
        productComponentId = productComponentId,
        recordedAt = recordedAt,
        quantity = quantity,
        note = note
    )
}
