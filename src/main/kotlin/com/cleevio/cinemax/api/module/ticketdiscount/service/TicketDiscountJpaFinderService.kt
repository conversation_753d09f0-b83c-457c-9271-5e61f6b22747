package com.cleevio.cinemax.api.module.ticketdiscount.service

import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.exception.TicketDiscountNotFoundException
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

@Service
@Validated
class TicketDiscountJpaFinderService(
    private val repository: TicketDiscountRepository,
) {

    fun getById(id: UUID): TicketDiscount = repository.findById(id).getOrNull()
        ?: throw TicketDiscountNotFoundException()

    fun findNonDeletedById(id: UUID): TicketDiscount? = repository.findByIdAndDeletedAtIsNull(id)

    fun findAllNonDeletedById(ids: Set<UUID>): List<TicketDiscount> = repository.findAllByIdIn(ids)

    fun findAllFixedPriceDiscounts(): List<TicketDiscount> = repository.findAllFixedPriceDiscounts()

    fun findAllNonDeletedByCodeIn(codes: Set<String>): List<TicketDiscount> = repository.findAllByCodeInAndDeletedAtIsNull(codes)

    fun getNonDeletedById(id: UUID): TicketDiscount = findNonDeletedById(id) ?: throw TicketDiscountNotFoundException()

    fun findNonDeletedByCode(code: String): TicketDiscount? = repository.findByCodeAndDeletedAtIsNull(code)

    fun getNonDeletedByCode(code: String): TicketDiscount = findNonDeletedByCode(code)
        ?: throw TicketDiscountNotFoundException()
}
