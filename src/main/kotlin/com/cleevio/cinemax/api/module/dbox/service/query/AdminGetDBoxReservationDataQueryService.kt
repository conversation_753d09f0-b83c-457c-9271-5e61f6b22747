package com.cleevio.cinemax.api.module.dbox.service.query

import com.cleevio.cinemax.api.common.util.Symbol
import com.cleevio.cinemax.api.common.util.buildDateCondition
import com.cleevio.cinemax.api.module.basket.util.PAID_BASKET_STATES
import com.cleevio.cinemax.api.module.dbox.service.model.DBoxReservationDataXmlModel
import com.cleevio.cinemax.api.module.dbox.service.model.Reservation
import com.cleevio.cinemax.api.module.dbox.service.model.Reservations
import com.cleevio.cinemax.api.module.dbox.service.model.Result
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.api.module.ticketprice.constant.SeatSurchargeType
import com.cleevio.cinemax.psql.Tables.SCREENING
import com.cleevio.cinemax.psql.Tables.SEAT
import com.cleevio.cinemax.psql.Tables.TICKET
import org.jooq.DSLContext
import org.jooq.impl.DSL.exists
import org.jooq.impl.DSL.select
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.time.Clock
import java.time.LocalDateTime

@Service
@Validated
class AdminGetDBoxReservationDataQueryService(
    private val psqlDslContext: DSLContext,
    private val clock: Clock,
) {

    operator fun invoke(): DBoxReservationDataXmlModel {
        val today = LocalDateTime.now(clock).toLocalDate()
        val endDate = today.plusDays(7)

        val screeningConditions = listOfNotNull(
            // Include all screenings from today, even past ones within today
            buildDateCondition(
                date = today,
                column = SCREENING.DATE,
                symbol = Symbol.GREATER_OR_EQUAL
            ),
            buildDateCondition(
                date = endDate,
                column = SCREENING.DATE,
                symbol = Symbol.LESS_OR_EQUAL
            ),
            SCREENING.CANCELLED.isFalse,
            SCREENING.STOPPED.isFalse,
            SCREENING.DELETED_AT.isNull,
            SCREENING.STATE.eq(ScreeningState.PUBLISHED)
        )

        val hasPaidTicketAndDBoxSurcharge = exists(
            select(TICKET.ID)
                .from(TICKET)
                .join(TICKET.basketItem())
                .join(TICKET.basketItem().basket())
                .where(TICKET.basketItem().basket().STATE.`in`(PAID_BASKET_STATES))
                .and(TICKET.basketItem().CANCELLED_BASKET_ITEM_ID.isNull)
                .and(TICKET.basketItem().IS_CANCELLED.isFalse)
                .and(TICKET.DELETED_AT.isNull)
                .and(TICKET.SCREENING_ID.eq(SCREENING.ID))
                .and(TICKET.reservation().SEAT_ID.eq(SEAT.ID))
                .and(TICKET.ticketPrice().SEAT_SURCHARGE_TYPE.eq(SeatSurchargeType.DBOX))
                .and(
                    TICKET.ticketPrice().SEAT_SURCHARGE.isNotNull
                        .or(TICKET.ticketPrice().SEAT_SURCHARGE.gt(0.toBigDecimal()))
                )
        )

        val reservations = psqlDslContext
            .select(
                SCREENING.ORIGINAL_ID,
                SEAT.ORIGINAL_ID,
                SEAT.ROW,
                SEAT.NUMBER,
                hasPaidTicketAndDBoxSurcharge
            )
            .from(SCREENING)
            .join(SEAT).on(SEAT.AUDITORIUM_ID.eq(SCREENING.AUDITORIUM_ID))
            .where(screeningConditions)
            .and(SEAT.TYPE.eq(SeatType.DBOX))
            .and(SEAT.AUDITORIUM_LAYOUT_ID.eq(SCREENING.AUDITORIUM_LAYOUT_ID))
            .orderBy(SCREENING.ORIGINAL_ID.asc())
            .fetch()
            .map {
                Reservation(
                    presentationId = it[SCREENING.ORIGINAL_ID].toString(),
                    seatId = it[SEAT.ORIGINAL_ID].toString(),
                    seatRow = it[SEAT.ROW],
                    seatCol = it[SEAT.NUMBER],
                    // State: 0 = Not reserved, 1 = Reserved with paid ticket
                    state = if (it[hasPaidTicketAndDBoxSurcharge]) "1" else "0"
                )
            }

        return DBoxReservationDataXmlModel(
            result = Result(),
            reservations = Reservations(reservationList = reservations)
        )
    }
}
