package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJpaFinderService
import com.cleevio.cinemax.api.module.movie.service.MovieJpaFinderService
import com.cleevio.cinemax.api.module.screening.event.toMessagingEvent
import com.cleevio.cinemax.api.module.screening.service.command.SyncScreeningToHeadquartersCommand
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeJpaFinderService
import com.cleevio.cinemax.api.module.screeningtype.entity.ScreeningType
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeJpaFinderService
import com.cleevio.cinemax.api.module.screeningtypes.entity.ScreeningTypes
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesJpaFinderService
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.time.LocalDate
import java.util.UUID

@Service
@Validated
class ScreeningMessagingService(
    private val screeningFinderService: ScreeningJpaFinderService,
    private val screeningFeeFinderService: ScreeningFeeJpaFinderService,
    private val screeningTypeFinderService: ScreeningTypeJpaFinderService,
    private val screeningTypesFinderService: ScreeningTypesJpaFinderService,
    private val auditoriumFinderService: AuditoriumJpaFinderService,
    private val movieFinderService: MovieJpaFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val log = logger()

    fun syncScreeningToHeadquarters(@Valid command: SyncScreeningToHeadquartersCommand) {
        log.info("Syncing Screening id=${command.screeningId} to headquarters...")

        val screening = screeningFinderService.getNonDeletedById(command.screeningId)
        val screeningFee = screeningFeeFinderService.getByScreeningId(screening.id)
        val auditorium = auditoriumFinderService.getById(screening.auditoriumId)
        val movie = movieFinderService.getNonDeletedById(screening.movieId)

        val screeningTypes = screeningTypesFinderService.findAllByScreeningId(screening.id)
        val screeningTypeCodes = screeningTypeFinderService.findAllByIdIn(
            ids = screeningTypes.mapToSet { it.screeningTypeId }
        ).mapToSet { it.code }

        applicationEventPublisher.publishEvent(
            screening.toMessagingEvent(
                screeningFee = screeningFee,
                auditoriumOriginalCode = auditorium.originalCode,
                movieMessagingCode = movie.messagingCode,
                screeningTypeCodes = screeningTypeCodes
            )
        )
    }

    fun syncScreeningsToHeadquarters(
        dateFrom: LocalDate,
        dateTo: LocalDate,
    ) {
        val screenings = screeningFinderService.findAllNonDeletedByDateIn(
            dateFrom = dateFrom,
            dateTo = dateTo
        )

        val screeningIds = screenings.mapToSet { it.id }
        val screeningIdToScreeningFee = screeningFeeFinderService.findAllByScreeningIdIn(
            screeningIds = screeningIds
        ).associateBy { it.screeningId }
        val auditoriumIdToAuditorium = auditoriumFinderService.findAll().associateBy { it.id }
        val movieIdToMovie = movieFinderService.findAllNonDeletedByIdIn(
            ids = screenings.mapToSet { it.movieId }
        ).associateBy { it.id }

        val screeningIdToScreeningTypesList: Map<UUID, List<ScreeningTypes>> = screeningTypesFinderService.findAllByScreeningIdIn(
            screeningIds = screeningIds
        ).groupBy { it.screeningId }
        val screeningTypeIdToScreeningType: Map<UUID, ScreeningType> = screeningTypeFinderService.findAll().associateBy { it.id }
        val screeningIdToScreeningTypeCodes: Map<UUID, List<String>> =
            screeningIdToScreeningTypesList.mapValues { (_, screeningTypesList) ->
                screeningTypesList.mapNotNull { screeningTypes ->
                    screeningTypeIdToScreeningType[screeningTypes.screeningTypeId]?.code
                }
            }

        screenings.forEach { screening ->
            applicationEventPublisher.publishEvent(
                screening.toMessagingEvent(
                    screeningFee = screeningIdToScreeningFee[screening.id]
                        ?: error("ScreeningFee for Screening messaging not found."),
                    auditoriumOriginalCode = auditoriumIdToAuditorium[screening.auditoriumId]?.originalCode
                        ?: error("Auditorium for Screening messaging not found."),
                    movieMessagingCode = movieIdToMovie[screening.movieId]?.messagingCode
                        ?: error("Movie for Screening messaging not found."),
                    screeningTypeCodes = screeningIdToScreeningTypeCodes[screening.id]?.toSet() ?: setOf()
                )
            )
        }
    }
}
