package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.ExportLanguage
import com.cleevio.cinemax.api.common.constant.ExportableBoolean
import com.cleevio.cinemax.api.common.service.model.ExportMainHeaderModel
import com.cleevio.cinemax.api.common.service.model.ExportResultModel
import com.cleevio.cinemax.api.common.service.model.ListExportModel
import com.cleevio.cinemax.api.common.util.columnHeader
import com.cleevio.cinemax.api.common.util.createBasicCellStyle
import com.cleevio.cinemax.api.common.util.createCell
import com.cleevio.cinemax.api.common.util.formatToCzSK
import com.cleevio.cinemax.api.common.util.mainHeader
import com.cleevio.cinemax.api.common.util.toDecimalCommaString
import com.cleevio.cinemax.api.common.util.toExportableBoolean
import com.cleevio.cinemax.api.common.util.toFourPlaces
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.service.StockMovementViewModelType.INPUT_VIEW_MODEL
import com.cleevio.cinemax.api.module.stockmovement.service.StockMovementViewModelType.OUTPUT_VIEW_MODEL
import com.cleevio.cinemax.api.module.stockmovement.service.model.StockMovementExportRecordModel
import org.apache.poi.xssf.streaming.SXSSFWorkbook
import org.springframework.stereotype.Component
import java.io.ByteArrayInputStream
import java.io.ByteArrayOutputStream
import java.time.LocalDate
import java.time.LocalDateTime

@Component
class StockMovementXlsxExportResultMapper(
    private val cinemaxConfigProperties: CinemaxConfigProperties,
) {

    fun mapToExportResultModel(
        data: List<StockMovementExportRecordModel>,
        viewModelType: StockMovementViewModelType,
        username: String,
        recordedAtFrom: LocalDateTime?,
        recordedAtTo: LocalDateTime?,
    ): ExportResultModel {
        val exportLanguage = ExportLanguage.fromBusinessCountry(cinemaxConfigProperties.businessCountry)

        val listExportModel = ListExportModel(
            header = getStockMovementExportHeader(
                username = username,
                recordedAtFromDate = recordedAtFrom?.toLocalDate(),
                recordedAtToDate = recordedAtTo?.toLocalDate(),
                branchName = cinemaxConfigProperties.branchName,
                viewModelType = viewModelType
            ),
            columnNames = getStockMovementExportColumnNames(viewModelType),
            columnData = data.map {
                when (viewModelType) {
                    INPUT_VIEW_MODEL -> it.toInputStockMovementExportViewModel()
                    OUTPUT_VIEW_MODEL -> it.toOutputStockMovementExportViewModel()
                }
            }
        )

        val columnTitles = listExportModel.columnNames[exportLanguage] ?: error("Export language for column titles not found.")
        val mainHeader = listExportModel.header[exportLanguage] ?: error("Export language for main header not found.")

        val workbook = SXSSFWorkbook(50)
        try {
            val cellStyle = workbook.createBasicCellStyle()

            val sheet = workbook.createSheet((mainHeader as ExportMainHeaderModel).exportTitle)
            sheet.mainHeader(mainHeader = mainHeader)
            sheet.columnHeader(columnTitles = columnTitles)

            var rowNum = 5
            for (stockMovement in listExportModel.columnData) {
                val row = sheet.createRow(rowNum++)
                when (viewModelType) {
                    INPUT_VIEW_MODEL -> {
                        val item = stockMovement as InputStockMovementExportViewModel
                        with(row) {
                            createCell(0, item.recordedAt, cellStyle)
                            createCell(1, item.productComponentOriginalCode, cellStyle)
                            createCell(2, item.productComponentTitle, cellStyle)
                            createCell(3, item.quantityWithUnit, cellStyle)
                            createCell(4, item.receiptNumber ?: "", cellStyle)
                            createCell(5, item.supplierTitle ?: "", cellStyle)
                        }
                    }

                    OUTPUT_VIEW_MODEL -> {
                        val item = stockMovement as OutputStockMovementExportViewModel
                        with(row) {
                            createCell(0, item.recordedAt, cellStyle)
                            createCell(1, item.productComponentOriginalCode, cellStyle)
                            createCell(2, item.productComponentTitle, cellStyle)
                            createCell(3, item.quantityWithUnit, cellStyle)
                            createCell(4, item.movementType.getLocalizedNames()[exportLanguage] ?: "", cellStyle)
                            createCell(5, item.isSale.getLocalizedNames()[exportLanguage] ?: "", cellStyle)
                        }
                    }
                }
            }

            for (i in 0..columnTitles.size) {
                sheet.trackColumnForAutoSizing(i)
                sheet.autoSizeColumn(i)
            }

            sheet.setColumnWidth(2, 8000)
            sheet.setColumnWidth(mainHeader.dataColumnsCount - 2, 5000)

            ByteArrayOutputStream().use { outputStream ->
                workbook.write(outputStream)

                return ExportResultModel(
                    inputStream = ByteArrayInputStream(outputStream.toByteArray()),
                    size = outputStream.size().toLong()
                )
            }
        } finally {
            workbook.dispose()
        }
    }

    private fun getStockMovementExportHeader(
        username: String,
        recordedAtFromDate: LocalDate?,
        recordedAtToDate: LocalDate?,
        branchName: String,
        viewModelType: StockMovementViewModelType,
    ): Map<ExportLanguage, ExportMainHeaderModel> = mapOf(
        ExportLanguage.CZECH to ExportMainHeaderModel(
            branch = branchName,
            branchLabel = "Kino",
            dateLabel = "Datum",
            userLabel = "Uživatel",
            username = username,
            exportTitle = when (viewModelType) {
                INPUT_VIEW_MODEL -> "Seznam příjmů na sklad"
                OUTPUT_VIEW_MODEL -> "Seznam výdajů ze skladu"
            },
            exportSubtitleFrom = "Vytvořeno od",
            exportDateFrom = recordedAtFromDate,
            exportSubtitleTo = "do",
            exportDateTo = recordedAtToDate,
            timeLabel = "Čas",
            dataColumnsCount = 6
        ),
        ExportLanguage.SLOVAK to ExportMainHeaderModel(
            branch = branchName,
            branchLabel = "Kino",
            dateLabel = "Dátum",
            userLabel = "Používateľ",
            username = username,
            exportTitle = when (viewModelType) {
                INPUT_VIEW_MODEL -> "Zoznam príjmov na sklad"
                OUTPUT_VIEW_MODEL -> "Zoznam výdajov zo skladu"
            },
            exportSubtitleFrom = "Vytvorené od",
            exportDateFrom = recordedAtFromDate,
            exportSubtitleTo = "do",
            exportDateTo = recordedAtToDate,
            timeLabel = "Čas",
            dataColumnsCount = 6
        )
    )

    private fun getStockMovementExportColumnNames(viewModel: StockMovementViewModelType): Map<ExportLanguage, List<String>> =
        when (viewModel) {
            INPUT_VIEW_MODEL ->
                mapOf(
                    ExportLanguage.CZECH to listOf(
                        "Datum pohybu",
                        "Číslo zboží",
                        "Název zboží",
                        "Množství",
                        "Číslo dokladu",
                        "Dodavatel"
                    ),
                    ExportLanguage.SLOVAK to listOf(
                        "Dátum pohybu",
                        "Číslo tovaru",
                        "Názov tovaru",
                        "Množstvo",
                        "Číslo dokladu",
                        "Dodávateľ"
                    )
                )

            OUTPUT_VIEW_MODEL ->
                mapOf(
                    ExportLanguage.CZECH to listOf(
                        "Datum pohybu",
                        "Číslo zboží",
                        "Název zboží",
                        "Množství",
                        "Druh pohybu",
                        "Predaj"
                    ),
                    ExportLanguage.SLOVAK to listOf(
                        "Dátum pohybu",
                        "Číslo tovaru",
                        "Názov tovaru",
                        "Množstvo",
                        "Druh pohybu",
                        "Predaj"
                    )
                )
        }
}

private fun StockMovementExportRecordModel.toInputStockMovementExportViewModel() =
    InputStockMovementExportViewModel(
        recordedAt = recordedAt.formatToCzSK(),
        productComponentOriginalCode = productComponentOriginalCode,
        productComponentTitle = productComponentTitle,
        quantityWithUnit = "${quantity.toFourPlaces().toDecimalCommaString()} ${unit.mssqlValue}",
        receiptNumber = receiptNumber,
        supplierTitle = supplierTitle
    )

private fun StockMovementExportRecordModel.toOutputStockMovementExportViewModel() =
    OutputStockMovementExportViewModel(
        recordedAt = recordedAt.formatToCzSK(),
        productComponentOriginalCode = productComponentOriginalCode,
        productComponentTitle = productComponentTitle,
        quantityWithUnit = "${quantity.toFourPlaces().toDecimalCommaString()} ${unit.mssqlValue}",
        movementType = type,
        isSale = (type == StockMovementType.PRODUCT_SALES).toExportableBoolean()
    )

data class InputStockMovementExportViewModel(
    val recordedAt: String,
    val productComponentOriginalCode: String,
    val productComponentTitle: String,
    val quantityWithUnit: String,
    val receiptNumber: String?,
    val supplierTitle: String?,
)

data class OutputStockMovementExportViewModel(
    val recordedAt: String,
    val productComponentOriginalCode: String,
    val productComponentTitle: String,
    val quantityWithUnit: String,
    val movementType: StockMovementType,
    val isSale: ExportableBoolean,
)
