package com.cleevio.cinemax.api.module.ticket.entity

import com.cleevio.cinemax.api.common.entity.DeletableEntity
import jakarta.persistence.Column
import java.util.UUID
import jakarta.persistence.Entity as JpaEntity

@JpaEntity
class Ticket(
    id: UUID = UUID.randomUUID(),

    @Column(name = "original_id")
    var originalId: Int? = null,

    @Column(name = "screening_id", nullable = false)
    var screeningId: UUID,

    @Column(name = "reservation_id", nullable = false)
    var reservationId: UUID,

    @Column(name = "ticket_price_id", nullable = false)
    var ticketPriceId: UUID,

    @Column(name = "ticket_discount_primary_id")
    var ticketDiscountPrimaryId: UUID? = null,

    @Column(name = "ticket_discount_secondary_id")
    var ticketDiscountSecondaryId: UUID? = null,

    @Column(name = "receipt_number")
    var receiptNumber: String? = null,

    @Column(name = "is_group_ticket")
    var isGroupTicket: Boolean,

    @Column(name = "is_used")
    var isUsed: Boolean,

    @Column(name = "includes_3d_glasses")
    var includes3dGlasses: Boolean,
) : DeletableEntity(id) {

    fun isMissingReceiptNumber() = receiptNumber == null
}
