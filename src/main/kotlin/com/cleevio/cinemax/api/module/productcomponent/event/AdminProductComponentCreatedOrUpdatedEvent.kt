package com.cleevio.cinemax.api.module.productcomponent.event

import com.cleevio.cinemax.api.common.integration.pubsub.constant.MessageType
import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.util.toJsonString
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.cleevio.cinemax.api.module.productcomponent.entity.ProductComponent
import com.fasterxml.jackson.annotation.JsonInclude
import java.math.BigDecimal
import java.util.UUID

@JsonInclude(JsonInclude.Include.NON_NULL)
data class AdminProductComponentCreatedOrUpdatedEvent(
    val code: String,
    val productComponentCategoryId: UUID,
    val title: String,
    val unit: ProductComponentUnit,
    val purchasePrice: BigDecimal,
    val active: Boolean,
    val taxRateOverride: Int?,
) {
    fun toMessagePayload() = MessagePayload(
        id = UUID.randomUUID(),
        type = MessageType.PRODUCT_COMPONENT_CREATED_OR_UPDATED,
        data = this.toJsonString()
    )
}

fun ProductComponent.toMessagingEvent() = AdminProductComponentCreatedOrUpdatedEvent(
    code = code,
    productComponentCategoryId = productComponentCategoryId,
    title = title,
    unit = unit,
    purchasePrice = purchasePrice,
    active = active,
    taxRateOverride = taxRateOverride
)
