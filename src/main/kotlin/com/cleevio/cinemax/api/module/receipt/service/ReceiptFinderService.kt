package com.cleevio.cinemax.api.module.receipt.service

import com.cleevio.cinemax.api.module.receipt.constant.ReceiptType
import com.cleevio.cinemax.api.module.receipt.entity.Receipt
import com.cleevio.cinemax.api.module.receipt.exception.ReceiptNotFoundException
import org.springframework.stereotype.Service
import java.util.UUID
import kotlin.jvm.optionals.getOrNull

@Service
class ReceiptFinderService(
    private val receiptRepository: ReceiptRepository,
) {

    fun findById(id: UUID): Receipt? = receiptRepository.findById(id).getOrNull()

    fun getById(id: UUID): Receipt = findById(id) ?: throw ReceiptNotFoundException()

    fun findLatestByBasketId(basketId: UUID): Receipt? = receiptRepository.findTopByBasketIdAndTypeOrderByCreatedAtDesc(
        basketId = basketId,
        type = ReceiptType.RECEIPT
    )

    fun findLatestCancelledByBasketId(basketId: UUID): Receipt? = receiptRepository.findTopByBasketIdAndTypeOrderByCreatedAtDesc(
        basketId = basketId,
        type = ReceiptType.CANCELLATION
    )
}
