package com.cleevio.cinemax.api.module.table.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.SimplePage
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.common.util.SimplePageMapper
import com.cleevio.cinemax.api.module.posconfiguration.constant.TablesType
import com.cleevio.cinemax.api.module.table.controller.dto.TableSearchResponse
import com.cleevio.cinemax.api.module.table.controller.mapper.TableResponseMapper
import com.cleevio.cinemax.api.module.table.service.TableFinderService
import com.cleevio.cinemax.api.module.table.service.query.TableFilter
import com.cleevio.cinemax.api.module.table.util.applyBasketCreatedAtSortToSeats
import com.cleevio.cinemax.api.module.table.util.validateTableFilter
import com.cleevio.cinemax.psql.tables.TableColumnNames
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController

@Tag(name = "POS Tables")
@RestController
@RequestMapping("/pos-app/tables")
class TableController(
    private val tableFinderService: TableFinderService,
    private val tableResponseMapper: TableResponseMapper,
) {

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchTables(
        @ParameterObject
        @PageableDefault(size = Integer.MAX_VALUE, sort = [TableColumnNames.ORDER])
        pageable: Pageable,
        @RequestParam tablesType: TablesType,
        @RequestParam includeSeats: Boolean,
    ): SimplePage<TableSearchResponse> {
        val tableFilter = TableFilter(tablesType, includeSeats).also {
            validateTableFilter(it)
        }

        val tablesPage = tableFinderService.search(SearchQueryDeprecated(pageable, tableFilter))
        val tableResponses = tableResponseMapper.mapList(
            tables = tablesPage.content,
            tablesType = tablesType,
            applyBasketCreatedAtSort = applyBasketCreatedAtSortToSeats(pageable)
        )
        return SimplePageMapper.mapToSimplePage(tablesPage, tableResponses)
    }
}
