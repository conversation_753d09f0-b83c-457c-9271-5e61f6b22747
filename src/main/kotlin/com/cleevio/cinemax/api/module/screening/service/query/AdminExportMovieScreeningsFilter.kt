package com.cleevio.cinemax.api.module.screening.service.query

import com.cleevio.cinemax.api.common.constant.ExportFormat
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.ticketprice.constant.ServiceFeeType
import com.cleevio.cinemax.api.module.ticketprice.constant.SurchargeType
import java.time.LocalDateTime
import java.util.UUID

data class AdminExportMovieScreeningsFilter(
    val basketPaidAtFrom: LocalDateTime? = null,
    val basketPaidAtTo: LocalDateTime? = null,
    val basketUpdatedBy: Set<String>? = null,
    val basketStates: Set<BasketState>? = null,
    val basketVariableSymbol: String? = null,
    val screeningDateTimeFrom: LocalDateTime? = null,
    val screeningDateTimeTo: LocalDateTime? = null,
    val auditoriumIds: Set<UUID>? = null,
    val screeningIds: Set<UUID>? = null,
    val movieIds: Set<UUID>? = null,
    val distributorIds: Set<UUID>? = null,
    val posConfigurationIds: Set<UUID>? = null,
    val paymentTypes: Set<PaymentType>? = null,
    val ticketUsed: Boolean? = null,
    val ticketIncludes3dGlasses: Boolean? = null,
    val surchargeTypes: Set<SurchargeType>? = null,
    val serviceFeeTypes: Set<ServiceFeeType>? = null,
    // TODO: this filter is not implemented!
    val screeningTypeIds: Set<UUID>? = null,
    val technologyIds: Set<UUID>? = null,
    val productionIds: Set<UUID>? = null,
    val ticketReceiptNumbers: Set<String>? = null,
    val discountCardCodes: Set<String>? = null,
    val discountCardTitles: Set<String>? = null,
    val isDiscounted: Boolean? = null,
    val primaryTicketDiscountIds: Set<UUID>? = null,
    val secondaryTicketDiscountIds: Set<UUID>? = null,
    val isCancelled: Boolean? = null,
    val branchIds: Set<UUID>? = null,
) {
    fun toQuery(exportFormat: ExportFormat, username: String) = AdminExportMovieScreeningsQuery(
        filter = this,
        exportFormat = exportFormat,
        username = username
    )
}
