package com.cleevio.cinemax.api.module.product.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.CONFLICT)
class PackagingDepositProductAlreadyExistsException : ApiException(
    Module.PRODUCT,
    ProductErrorType.PACKAGING_DEPOSIT_PRODUCT_ALREADY_EXISTS
)
