package com.cleevio.cinemax.api.module.ticketdiscount.controller.dto

import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import java.time.LocalDateTime
import java.util.UUID

data class AdminSearchTicketDiscountsResponse(
    val id: UUID,
    val title: String? = null,
    val code: String,
    val type: TicketDiscountType,
    val usageType: TicketDiscountUsageType,
    val voucherOnly: Boolean,
    val active: Boolean,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)
