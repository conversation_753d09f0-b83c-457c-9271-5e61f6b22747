package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.common.util.fromJsonString
import com.cleevio.cinemax.api.common.util.letTwo
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.employee.entity.Employee
import com.cleevio.cinemax.api.module.employee.service.EmployeeFinderService
import com.cleevio.cinemax.api.module.employee.service.EmployeeMssqlBuffetFinderRepository
import com.cleevio.cinemax.api.module.employee.service.EmployeeMssqlCinemaxFinderRepository
import com.cleevio.cinemax.api.module.employee.service.EmployeeService
import com.cleevio.cinemax.api.module.employee.service.MANAGER_ACCESS_RIGHT
import com.cleevio.cinemax.api.module.employee.service.command.UpdateEmployeeOriginalIdsCommand
import com.cleevio.cinemax.api.module.outboxevent.constant.BUFFET_DB
import com.cleevio.cinemax.api.module.outboxevent.constant.CINEMAX_DB
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.outboxevent.model.EmployeeCreatedOrUpdatedEventData
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.PRAVA
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.PRAVUZ
import org.jooq.DSLContext
import org.jooq.impl.DSL.ltrim
import org.jooq.impl.DSL.rtrim
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import kotlin.math.min
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.RUZIV as BUFFET_RUZIV
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.records.RuzivRecord as BuffetRuzivRecord
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.RUZIV as CINEMAX_RUZIV
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.records.RuzivRecord as CinemaxRuzivRecord

@Service
class EmployeeCreatedOrUpdatedEventProcessor(
    private val employeeFinderService: EmployeeFinderService,
    private val employeeService: EmployeeService,
    private val employeeMssqlCinemaxFinderRepository: EmployeeMssqlCinemaxFinderRepository,
    private val employeeMssqlBuffetFinderRepository: EmployeeMssqlBuffetFinderRepository,
    private val mssqlCinemaxDslContext: DSLContext,
    private val mssqlBuffetDslContext: DSLContext,
) {
    private val log = logger()

    @Transactional(transactionManager = "mssqlCinemaxTransactionManager")
    fun process(event: OutboxEvent): Int {
        log.info("Starting to process OutboxEvent=$event.")

        return runCatching {
            val employee = employeeFinderService.getNonDeletedById(event.entityId)
            val eventData: EmployeeCreatedOrUpdatedEventData = event.data.fromJsonString()

            letTwo(employee.originalId, employee.originalBuffetId) { originalId, originalBuffetId ->
                if (!employeeMssqlCinemaxFinderRepository.existsByOriginalId(originalId)) {
                    error("MSSQL ruziv record with originalId $originalId not found in $CINEMAX_DB.")
                }
                if (!employeeMssqlBuffetFinderRepository.existsByOriginalId(originalBuffetId)) {
                    error("MSSQL ruziv record with originalId $originalBuffetId not found in $BUFFET_DB.")
                }

                val cinemaxUpdateResult = updateMssqlCinemaxEmployeeRecord(employee)
                val buffetUpdateResult = updateMssqlBuffetEmployeeRecord(employee)

                min(cinemaxUpdateResult, buffetUpdateResult)
            } ?: run {
                val whitespacePaddedPassword = eventData.encodedPassword?.let {
                    padWithWhitespaceCharactersIfNeeded(it)
                } ?: error("Employee password must not be null.")

                val originalId = createMssqlCinemaxEmployeeRecord(employee, whitespacePaddedPassword)
                    ?: error("MSSQL $CINEMAX_DB.ruziv record was not created or its originalId could not be fetched.")
                val originalBuffetId = createMssqlBuffetEmployeeRecord(employee, whitespacePaddedPassword)
                    ?: error("MSSQL $BUFFET_DB.ruziv record was not created or its originalId could not be fetched.")

                if (employee.role == EmployeeRole.CASHIER) {
                    createMssqlCinemaxEmployeeRoleRecord(originalId)
                }

                employeeService.updateEmployeeOriginalIds(
                    UpdateEmployeeOriginalIdsCommand(
                        employeeId = employee.id,
                        originalId = originalId,
                        originalBuffetId = originalBuffetId
                    )
                )
            }
            1
        }.onFailure { ex ->
            log.error("Failed to process OutboxEvent: ${ex.message}", ex)
        }.getOrDefault(0)
    }

    private fun createMssqlCinemaxEmployeeRecord(
        employee: Employee,
        encodedPassword: String,
    ): Int? = mssqlBuffetDslContext.insertInto(CINEMAX_RUZIV)
        .set(prepareMssqlCinemaxEmployeeRecord(employee, encodedPassword))
        .returningResult(CINEMAX_RUZIV.RUZIVID)
        .fetchOneInto(Int::class.java)

    private fun createMssqlCinemaxEmployeeRoleRecord(originalId: Int) {
        val mssqlRoleId = mssqlBuffetDslContext.select(PRAVA.PRAVAID)
            .from(PRAVA)
            .where(ltrim(rtrim(PRAVA.KLIC)).eq(MANAGER_ACCESS_RIGHT))
            .fetchOneInto(Int::class.java)

        mssqlBuffetDslContext.insertInto(PRAVUZ)
            .set(PRAVUZ.PRAVAID, mssqlRoleId)
            .set(PRAVUZ.RUZIVID, originalId)
            .execute()
    }

    private fun createMssqlBuffetEmployeeRecord(
        employee: Employee,
        encodedPassword: String,
    ): Int? = mssqlBuffetDslContext.insertInto(BUFFET_RUZIV)
        .set(prepareMssqlBuffetEmployeeRecord(employee, encodedPassword))
        .returningResult(BUFFET_RUZIV.RUZIVID)
        .fetchOneInto(Int::class.java)

    private fun updateMssqlCinemaxEmployeeRecord(employee: Employee): Int = mssqlCinemaxDslContext
        .update(CINEMAX_RUZIV)
        .set(prepareMssqlBuffetEmployeeRecord(employee = employee, encodedPassword = null))
        .where(
            CINEMAX_RUZIV.RUZIVID.eq(
                employee.originalId?.toShort() ?: error("Can't update ruziv with null originalId.")
            )
        )
        .execute()

    private fun updateMssqlBuffetEmployeeRecord(employee: Employee): Int = mssqlBuffetDslContext
        .update(BUFFET_RUZIV)
        .set(prepareMssqlBuffetEmployeeRecord(employee = employee, encodedPassword = null))
        .where(
            BUFFET_RUZIV.RUZIVID.eq(
                employee.originalBuffetId?.toShort() ?: error("Can't update ruziv with null originalBuffetId.")
            )
        )
        .execute()

    private fun prepareMssqlCinemaxEmployeeRecord(employee: Employee, encodedPassword: String?): CinemaxRuzivRecord =
        mssqlCinemaxDslContext.newRecord(CINEMAX_RUZIV).also {
            it.uziv = employee.username
            it.jmeno = employee.fullName
            encodedPassword?.let { password -> it.heslo = password }
            it.pokladna = employee.posName
            it.datod = employee.accessibleAt
            it.zuziv = employee.updatedBy
            it.zcas = employee.updatedAt

            // unused columns need to be set with default values
            it.zapis = false
            it.datdo = null
            it.pokd = 0
            it.pokz = 0
            it.uzpokl = null
            it.prace = ""
            it.vstup = false
            it.old = 0
        }

    private fun prepareMssqlBuffetEmployeeRecord(employee: Employee, encodedPassword: String?): BuffetRuzivRecord =
        mssqlBuffetDslContext.newRecord(BUFFET_RUZIV).also {
            it.uziv = employee.username
            it.jmeno = employee.fullName
            encodedPassword?.let { password -> it.heslo = password }
            it.pokladna = employee.posName
            it.datod = employee.accessibleAt
            it.zuziv = employee.updatedBy
            it.zcas = employee.updatedAt

            // unused columns need to be set with default values
            it.zapis = false
            it.sklad = "01"
            it.datdo = null
            it.pokd = 0
            it.pokz = 0
            it.uzpokl = null
            it.prace = ""
            it.vstup = false
            it.old = 0
        }
}
