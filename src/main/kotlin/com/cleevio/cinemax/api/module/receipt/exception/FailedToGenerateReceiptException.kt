package com.cleevio.cinemax.api.module.receipt.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.INTERNAL_SERVER_ERROR)
class FailedToGenerateReceiptException : ApiException(
    Module.RECEIPT,
    ReceiptErrorType.FAILED_TO_FIND_GENERATED_RECEIPT
)
