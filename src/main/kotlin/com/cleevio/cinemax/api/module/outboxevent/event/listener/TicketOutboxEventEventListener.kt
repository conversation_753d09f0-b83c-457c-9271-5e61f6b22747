package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventsCommand
import com.cleevio.cinemax.api.module.ticket.event.TicketsMovedEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.outbox-event.ticket.listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class TicketOutboxEventEventListener(
    private val outboxEventService: OutboxEventService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToTicketsMovedEvent(event: TicketsMovedEvent) {
        outboxEventService.createOutboxEvents(
            CreateOutboxEventsCommand(
                entityIds = event.ticketIds,
                type = OutboxEventType.TICKET_MOVED
            )
        )
    }
}
