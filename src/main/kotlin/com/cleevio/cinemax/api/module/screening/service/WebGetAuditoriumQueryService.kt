package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.controller.dto.WebGetAuditoriumDetailResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.WebGetAuditoriumFeesResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.WebGetAuditoriumResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.WebGetAuditoriumSeatResponse
import com.cleevio.cinemax.api.module.screening.controller.dto.WebPriceCategoryItemResponse
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.exception.ScreeningWebPriceNotFoundException
import com.cleevio.cinemax.api.module.screening.service.query.WebGetAuditoriumQuery
import com.cleevio.cinemax.api.module.seat.constant.SeatType
import com.cleevio.cinemax.psql.Tables.PRICE_CATEGORY_ITEM
import com.cleevio.cinemax.psql.Tables.SCREENING
import com.cleevio.cinemax.psql.Tables.SCREENING_FEE
import com.cleevio.cinemax.psql.Tables.SEAT
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.jooq.Record
import org.jooq.impl.DSL.multiset
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal

@Service
@Validated
class WebGetAuditoriumQueryService(
    private val psqlDslContext: DSLContext,
) {

    operator fun invoke(
        @Valid query: WebGetAuditoriumQuery,
    ): WebGetAuditoriumResponse {
        val screeningConditions = listOf(
            SCREENING.ORIGINAL_ID.eq(query.screeningOriginalId),
            SCREENING.DELETED_AT.isNull,
            SCREENING.STOPPED.isFalse,
            SCREENING.CANCELLED.isFalse,
            SCREENING.PUBLISH_ONLINE.isTrue,
            SCREENING.STATE.eq(ScreeningState.PUBLISHED)
        )

        // Get screening and related data
        return psqlDslContext.select(
            SCREENING.ORIGINAL_ID,
            SCREENING.auditorium().ID,
            SCREENING.auditorium().ORIGINAL_CODE,
            SCREENING.auditorium().CODE,
            SCREENING.auditorium().TITLE,
            SCREENING_FEE.SURCHARGE_VIP,
            SCREENING_FEE.SURCHARGE_PREMIUM,
            SCREENING_FEE.SURCHARGE_IMAX,
            SCREENING_FEE.SURCHARGE_ULTRA_X,
            SCREENING_FEE.SURCHARGE_D_BOX,
            SCREENING_FEE.SERVICE_FEE_VIP,
            SCREENING_FEE.SERVICE_FEE_PREMIUM,
            SCREENING_FEE.SERVICE_FEE_IMAX,
            SCREENING_FEE.SERVICE_FEE_ULTRA_X,
            SCREENING_FEE.SERVICE_FEE_GENERAL,
            priceCategoryItems,
            seats
        ).from(SCREENING).join(SCREENING.auditorium()).join(SCREENING.priceCategory()).leftJoin(SCREENING_FEE)
            .on(SCREENING_FEE.SCREENING_ID.eq(SCREENING.ID))
            .where(screeningConditions)
            .fetchOne()
            ?.let { record ->
                WebGetAuditoriumResponse(
                    originalId = record[SCREENING.ORIGINAL_ID].toLong(),
                    priceCategoryItems = record[priceCategoryItems]?.map { priceCategoryItem ->
                        WebPriceCategoryItemResponse(
                            number = priceCategoryItem[PRICE_CATEGORY_ITEM.NUMBER],
                            price = priceCategoryItem[PRICE_CATEGORY_ITEM.PRICE],
                            isDiscounted = priceCategoryItem[PRICE_CATEGORY_ITEM.DISCOUNTED]
                        )
                    } ?: emptyList(),
                    fees = WebGetAuditoriumFeesResponse(
                        surchargeVip = record[SCREENING_FEE.SURCHARGE_VIP] ?: BigDecimal.ZERO,
                        surchargePremium = record[SCREENING_FEE.SURCHARGE_PREMIUM] ?: BigDecimal.ZERO,
                        surchargeImax = record[SCREENING_FEE.SURCHARGE_IMAX] ?: BigDecimal.ZERO,
                        surchargeUltraX = record[SCREENING_FEE.SURCHARGE_ULTRA_X] ?: BigDecimal.ZERO,
                        serviceFeeVip = record[SCREENING_FEE.SERVICE_FEE_VIP] ?: BigDecimal.ZERO,
                        serviceFeePremium = record[SCREENING_FEE.SERVICE_FEE_PREMIUM] ?: BigDecimal.ZERO,
                        serviceFeeImax = record[SCREENING_FEE.SERVICE_FEE_IMAX] ?: BigDecimal.ZERO,
                        serviceFeeUltraX = record[SCREENING_FEE.SERVICE_FEE_ULTRA_X] ?: BigDecimal.ZERO,
                        surchargeDBox = record[SCREENING_FEE.SURCHARGE_D_BOX] ?: BigDecimal.ZERO,
                        serviceFeeGeneral = record[SCREENING_FEE.SERVICE_FEE_GENERAL] ?: BigDecimal.ZERO
                    ),
                    auditorium = WebGetAuditoriumDetailResponse(
                        originalCode = record[SCREENING.auditorium().ORIGINAL_CODE],
                        isImax = record[SCREENING.auditorium().CODE] == "IMAX",
                        seats = mapSeats(record)
                    )
                )
            } ?: throw ScreeningNotFoundException()
    }

    private val seats = multiset(
        psqlDslContext.select(
            SEAT.TYPE,
            SEAT.DOUBLE_SEAT_TYPE,
            SEAT.ROW,
            SEAT.NUMBER,
            SEAT.POSITION_LEFT,
            SEAT.POSITION_TOP
        ).from(SEAT)
            .where(
                SEAT.AUDITORIUM_ID.eq(SCREENING.AUDITORIUM_ID),
                SEAT.AUDITORIUM_LAYOUT_ID.eq(SCREENING.AUDITORIUM_LAYOUT_ID)
            )
    )

    private val priceCategoryItems = multiset(
        psqlDslContext.select(
            PRICE_CATEGORY_ITEM.PRICE,
            PRICE_CATEGORY_ITEM.NUMBER,
            PRICE_CATEGORY_ITEM.DISCOUNTED
        ).from(PRICE_CATEGORY_ITEM).where(
            SCREENING.PRICE_CATEGORY_ID.eq(PRICE_CATEGORY_ITEM.PRICE_CATEGORY_ID),
            PRICE_CATEGORY_ITEM.NUMBER.eq(PriceCategoryItemNumber.PRICE_20)
        )
    )

    private fun mapSeats(record: Record) =
        record[seats]?.map { seatRecord ->

            val seatBasePrice = record[priceCategoryItems].firstOrNull()?.let { it[PRICE_CATEGORY_ITEM.PRICE] }
                ?: throw ScreeningWebPriceNotFoundException()

            val seatSuperCharge = when (seatRecord[SEAT.TYPE]) {
                SeatType.PREMIUM_PLUS -> record[SCREENING_FEE.SURCHARGE_PREMIUM] ?: BigDecimal.ZERO
                SeatType.VIP -> record[SCREENING_FEE.SURCHARGE_VIP] ?: BigDecimal.ZERO
                SeatType.DBOX -> record[SCREENING_FEE.SURCHARGE_D_BOX] ?: BigDecimal.ZERO
                else -> BigDecimal.ZERO
            }

            val seatServiceFee = when (seatRecord[SEAT.TYPE]) {
                SeatType.PREMIUM_PLUS -> record[SCREENING_FEE.SERVICE_FEE_PREMIUM] ?: BigDecimal.ZERO
                SeatType.VIP -> record[SCREENING_FEE.SERVICE_FEE_VIP] ?: BigDecimal.ZERO
                else -> BigDecimal.ZERO
            }

            val setGeneralFee = record[SCREENING_FEE.SERVICE_FEE_GENERAL] ?: BigDecimal.ZERO

            val iMaxSuperCharge = if (record[SCREENING.auditorium().CODE] == "IMAX") {
                record[SCREENING_FEE.SURCHARGE_IMAX] ?: BigDecimal.ZERO
            } else {
                BigDecimal.ZERO
            }

            val iMaxServiceFee = if (record[SCREENING.auditorium().CODE] == "IMAX") {
                record[SCREENING_FEE.SERVICE_FEE_IMAX] ?: BigDecimal.ZERO
            } else {
                BigDecimal.ZERO
            }

            val ultraXServiceFee = record[SCREENING_FEE.SERVICE_FEE_ULTRA_X] ?: BigDecimal.ZERO
            val ultraXSurcharge = record[SCREENING_FEE.SURCHARGE_ULTRA_X] ?: BigDecimal.ZERO

            val hasDBoxZeroSurcharge = record[SCREENING_FEE.SURCHARGE_D_BOX]?.compareTo(BigDecimal.ZERO) == 0
            // handle spacial product requirement
            //  if the related ScreeningFee.surchargeDBox is 0, remap those seats to the PREMIUM_PLUS type
            val seatType = if (seatRecord[SEAT.TYPE] == SeatType.DBOX && hasDBoxZeroSurcharge) {
                SeatType.PREMIUM_PLUS
            } else {
                seatRecord[SEAT.TYPE]
            }

            WebGetAuditoriumSeatResponse(
                type = seatType,
                doubleSeatType = seatRecord[SEAT.DOUBLE_SEAT_TYPE],
                row = seatRecord[SEAT.ROW],
                number = seatRecord[SEAT.NUMBER],
                positionLeft = seatRecord[SEAT.POSITION_LEFT],
                positionTop = seatRecord[SEAT.POSITION_TOP],
                price = seatBasePrice +
                    setGeneralFee +
                    seatSuperCharge + seatServiceFee +
                    iMaxSuperCharge + iMaxServiceFee +
                    ultraXServiceFee + ultraXSurcharge
            )
        }?.toList() ?: emptyList()
}
