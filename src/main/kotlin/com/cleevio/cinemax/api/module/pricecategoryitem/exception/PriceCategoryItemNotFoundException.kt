package com.cleevio.cinemax.api.module.pricecategoryitem.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.NOT_FOUND)
class PriceCategoryItemNotFoundException : ApiException(
    Module.PRICE_CATEGORY_ITEM,
    PriceCategoryItemErrorType.PRICE_CATEGORY_ITEM_NOT_FOUND
)
