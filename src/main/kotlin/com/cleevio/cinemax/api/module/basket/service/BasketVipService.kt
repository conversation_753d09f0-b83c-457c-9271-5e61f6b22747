package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.common.constant.BASKET
import com.cleevio.cinemax.api.module.basket.constant.BasketLockValues.CLOSE_VIP_BASKET
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.exception.BasketNotFoundException
import com.cleevio.cinemax.api.module.basket.service.command.CloseVipBasketCommand
import com.cleevio.cinemax.api.module.basket.util.validateOpenBasketState
import com.cleevio.cinemax.api.module.table.event.TableBasketClosedLegacyEvent
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated

@Service
@Validated
class BasketVipService(
    private val basketFinderRepository: BasketFinderRepository,
    private val basketRepository: BasketRepository,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {

    @Transactional
    @Lock(BASKET, CLOSE_VIP_BASKET)
    fun closeVipBasket(
        @Valid
        @LockFieldParameter("basketId")
        command: CloseVipBasketCommand,
    ) {
        val basket = basketFinderRepository.findNonDeletedById(command.basketId)?.also {
            validateOpenBasketState(it)
        } ?: throw BasketNotFoundException()

        basketRepository.save(
            basket.apply {
                state = BasketState.CLOSED_LEGACY
            }
        )

        applicationEventPublisher.publishEvent(
            TableBasketClosedLegacyEvent(
                basketId = basket.id,
                tableId = command.tableId
            )
        )
    }
}
