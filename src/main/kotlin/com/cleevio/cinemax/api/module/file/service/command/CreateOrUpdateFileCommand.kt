package com.cleevio.cinemax.api.module.file.service.command

import com.cleevio.cinemax.api.common.service.CreateOrUpdateCommand
import com.cleevio.cinemax.api.module.file.constant.FileType
import jakarta.validation.constraints.NotBlank
import java.io.InputStream
import java.util.UUID

data class CreateOrUpdateFileCommand(
    override val id: UUID? = null,
    val originalId: Int,
    val type: FileType,

    @field:NotBlank
    val originalName: String,

    @field:NotBlank
    val extension: String,
    val inputStream: InputStream,
) : CreateOrUpdateCommand()
