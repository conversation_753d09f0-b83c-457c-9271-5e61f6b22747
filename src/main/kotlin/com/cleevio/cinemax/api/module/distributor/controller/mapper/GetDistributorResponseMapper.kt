package com.cleevio.cinemax.api.module.distributor.controller.mapper

import com.cleevio.cinemax.api.module.distributor.controller.dto.AdminGetDistributorResponse
import com.cleevio.cinemax.api.module.distributor.entity.Distributor

object GetDistributorResponseMapper {

    fun map(distributor: Distributor) = AdminGetDistributorResponse(
        id = distributor.id,
        code = distributor.code,
        disfilmCode = distributor.disfilmCode,
        title = distributor.title,
        addressStreet = distributor.addressStreet,
        addressCity = distributor.addressCity,
        addressPostCode = distributor.addressPostCode,
        contactName1 = distributor.contactName1,
        contactName2 = distributor.contactName2,
        contactName3 = distributor.contactName3,
        contactPhone1 = distributor.contactPhone1,
        contactPhone2 = distributor.contactPhone2,
        contactPhone3 = distributor.contactPhone3,
        contactEmails = distributor.contactEmails,
        bankName = distributor.bankName,
        bankAccount = distributor.bankAccount,
        idNumber = distributor.idNumber,
        taxIdNumber = distributor.taxIdNumber,
        vatRate = distributor.vatRate,
        note = distributor.note
    )
}
