package com.cleevio.cinemax.api.module.outboxevent.service

import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.employee.entity.Employee
import com.cleevio.cinemax.api.module.outboxevent.model.ProductSaleInsertQueryExecutionModel
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.mssql.rp_bufete.dbo.Tables.RPRODEJ
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.records.RprodejRecord
import org.jooq.DSLContext
import org.jooq.types.UByte
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class BasketToProductSalesMssqlService(
    private val mssqlBuffetDslContext: DSLContext,
    private val tableBasketMssqlRepository: TableBasketMssqlRepository,
) {
    private val log = logger()

    @Transactional(transactionManager = "mssqlBuffetTransactionManager")
    fun execute(
        executionModel: List<Pair<ProductSaleInsertQueryExecutionModel, RprodejRecord>>,
        tableOriginalId: Int?,
    ): List<Int> {
        val insertQueryResults = mutableListOf<Int>()

        executionModel
            .sortedBy { it.first.basketItem.createdAt }
            .forEach {
                val queryResult = if (it.first.basketItem.isCancellationItem()) {
                    val conditions = RPRODEJ.DOKLAD.eq(it.first.basketItem.productReceiptNumber)
                        .and(RPRODEJ.CENA.eq(it.first.basketItem.price))
                        .and(RPRODEJ.MNOZ.eq(it.first.basketItem.quantity))

                    log.info("Executing delete query for conditions $conditions.")

                    mssqlBuffetDslContext.deleteFrom(RPRODEJ).where(conditions).execute()
                } else {
                    log.info("Executing insert query for record: ${it.second}.")

                    mssqlBuffetDslContext.insertInto(RPRODEJ).set(it.second).execute()
                }

                insertQueryResults.add(queryResult)
                log.info("Query execution finished with result=$queryResult.")

                if (queryResult == 1) {
                    it.first.basketItem.originalId?.let { originalId ->
                        tableBasketMssqlRepository.deleteMssqlBasketItem(originalId)
                    }
                }
            }.also {
                tableOriginalId?.let { tableOriginalId ->
                    tableBasketMssqlRepository.updateMssqlTableState(
                        originalTableId = tableOriginalId,
                        openBasketExists = false
                    )
                }
            }

        return insertQueryResults
    }

    fun prepareProductSaleInsertRecord(
        product: Product,
        productBasketItem: BasketItem,
        taxRate: Int,
        basket: Basket,
        employee: Employee,
        relatedDiscountCard: DiscountCard?,
    ): RprodejRecord {
        val productOriginalId = requireNotNull(product.originalId) { "Attribute 'originalId' must not be null." }
        val originalProductId = if (productOriginalId < 0) -productOriginalId else productOriginalId

        return mssqlBuffetDslContext.newRecord(RPRODEJ).apply {
            rmenuid = if (productOriginalId > 0) originalProductId else 0
            rzboziid = 0
            rpoklid = 0
            doklad = productBasketItem.productReceiptNumber
            sklad = "01"
            cena = productBasketItem.price
            mnoz = productBasketItem.quantity
            dph = if (product.type == ProductType.PRODUCT_IN_PRODUCT) UByte.valueOf(0) else UByte.valueOf(taxRate)
            lvrat = false
            lhoto = basket.paymentType == PaymentType.CASH
            datprod = basket.paidAt
            datpokl = basket.paidAt
            uzivprod = employee.originalBuffetId
            uzivpokl = 0
            pokladna = employee.posName ?: employee.username
            zuziv = employee.username
            zcas = LocalDateTime.now()
            karta = if (relatedDiscountCard?.type == DiscountCardType.CARD) relatedDiscountCard.code else ""
            rzbozirid = if (productOriginalId < 0) originalProductId else 0
            pcid = 0
            voucher = if (relatedDiscountCard?.type == DiscountCardType.VOUCHER) relatedDiscountCard.code else ""
            ltablet = false
        }
    }
}
