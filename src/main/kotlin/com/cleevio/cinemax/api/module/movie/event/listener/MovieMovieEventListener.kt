package com.cleevio.cinemax.api.module.movie.event.listener

import com.cleevio.cinemax.api.module.movie.event.DISFilmMoviesCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.movie.service.MovieMessagingService
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.context.event.EventListener
import org.springframework.stereotype.Component

@Component
@ConditionalOnProperty(
    "cinemax.configuration.deployment-type",
    havingValue = "HEADQUARTERS"
)
class MovieMovieEventListener(
    private val movieMessagingService: MovieMessagingService,
) {

    @EventListener
    fun listenToDISFilmMoviesCreatedOrUpdatedEvent(event: DISFilmMoviesCreatedOrUpdatedEvent) {
        movieMessagingService.syncMoviesToBranches(ids = event.movieIds)
    }
}
