package com.cleevio.cinemax.api.module.employee.controller.dto

import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import com.cleevio.cinemax.api.module.employee.service.command.CreateOrUpdateEmployeeCommand
import java.time.LocalDateTime

data class CreateEmployeeRequest(
    val username: String,
    val fullName: String,
    val posName: String,
    val password: String,
    val role: EmployeeRole,
    val accessibleAt: LocalDateTime,
)

fun CreateEmployeeRequest.toCommand() = CreateOrUpdateEmployeeCommand(
    username = this.username,
    fullName = this.fullName,
    posName = this.posName,
    password = this.password,
    passwordReset = true,
    role = this.role,
    accessibleAt = this.accessibleAt
)
