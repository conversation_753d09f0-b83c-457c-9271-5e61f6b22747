package com.cleevio.cinemax.api.module.groupreservation.service

import com.cleevio.cinemax.api.module.groupreservation.constant.GroupReservationType
import com.cleevio.cinemax.api.module.groupreservation.entity.GroupReservation
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.time.LocalDateTime
import java.util.UUID

@Repository
interface GroupReservationRepository : JpaRepository<GroupReservation, UUID> {

    fun findByIdAndDeletedAtIsNull(id: UUID): GroupReservation?

    fun findByOriginalId(originalId: Int): GroupReservation?

    fun findByOriginalIdAndDeletedAtIsNull(originalId: Int): GroupReservation?

    fun findAllByExpiresAtBefore(startDate: LocalDateTime): List<GroupReservation>

    fun existsByIdAndDeletedAtIsNull(id: UUID): Boolean

    fun existsNonDeletedByIdAndType(id: UUID, type: GroupReservationType): Boolean
}
