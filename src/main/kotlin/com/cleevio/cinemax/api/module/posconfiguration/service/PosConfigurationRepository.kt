package com.cleevio.cinemax.api.module.posconfiguration.service

import com.cleevio.cinemax.api.module.posconfiguration.entity.PosConfiguration
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface PosConfigurationRepository : JpaRepository<PosConfiguration, UUID> {

    fun findByMacAddress(macAddress: String): PosConfiguration?

    @Query(
        """
            SELECT pc.* FROM pos_configuration pc WHERE type = 'ONLINE' LIMIT 1
        """,
        nativeQuery = true
    )
    fun findOnlinePosConfiguration(): PosConfiguration?

    @Query(
        """
            SELECT count(pc.id) > 0 FROM pos_configuration pc WHERE id = :id AND type = 'PHYSICAL'
        """,
        nativeQuery = true
    )
    fun validateIsPhysicalPOS(id: UUID): Boolean

    fun findByTitle(title: String): PosConfiguration?
}
