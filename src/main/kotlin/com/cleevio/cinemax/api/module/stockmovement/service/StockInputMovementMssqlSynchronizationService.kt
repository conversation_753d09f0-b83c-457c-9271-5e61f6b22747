package com.cleevio.cinemax.api.module.stockmovement.service

import com.cleevio.cinemax.api.common.constant.STOCK_MOVEMENT
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.productcomponent.service.ProductComponentJpaFinderService
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementLockValues.SYNCHRONIZE_STOCK_INPUT_MOVEMENT_FROM_MSSQL
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import com.cleevio.cinemax.api.module.stockmovement.service.command.CreateOrUpdateStockMovementCommand
import com.cleevio.cinemax.api.module.supplier.service.SupplierJpaFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.Rprij.RPRIJ
import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Rprij
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class StockInputMovementMssqlSynchronizationService(
    private val stockMovementService: StockMovementService,
    private val stockInputMovementMssqlFinderRepository: StockInputMovementMssqlFinderRepository,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    private val supplierJpaFinderService: SupplierJpaFinderService,
    private val productComponentJpaFinderService: ProductComponentJpaFinderService,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Rprij>(
    mssqlEntityName = RPRIJ.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.STOCK_INPUT_MOVEMENT
) {
    private val log = logger()

    @TryLock(STOCK_MOVEMENT, SYNCHRONIZE_STOCK_INPUT_MOVEMENT_FROM_MSSQL)
    fun synchronizeAll() = synchronizeAll {
        stockInputMovementMssqlFinderRepository.findAllStockInputMovementsByUpdatedAtGt(
            updatedAt = synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.STOCK_INPUT_MOVEMENT)
        )
    }

    override fun validateAndPersist(mssqlEntity: Rprij) {
        val supplier = supplierJpaFinderService.findNonDeletedByOriginalId(mssqlEntity.rdodid) ?: run {
            log.warn(
                "Supplier with originalId=${mssqlEntity.rdodid} not found. " +
                    "Skipping sync of Stock Input Movement originalId=${mssqlEntity.rprijid}."
            )
            return
        }

        val productComponent = productComponentJpaFinderService.findNonDeletedByOriginalId(mssqlEntity.rzboziid) ?: run {
            log.warn(
                "Product Component with originalId=${mssqlEntity.rzboziid} not found. " +
                    "Skipping sync of Stock Input Movement originalId=${mssqlEntity.rprijid}."
            )
            return
        }

        stockMovementService.syncCreateOrUpdateStockMovement(
            mapToCreateOrUpdateStockMovementCommand(
                mssqlEntity = mssqlEntity,
                supplierId = supplier.id,
                productComponentId = productComponent.id
            )
        )
    }

    private fun mapToCreateOrUpdateStockMovementCommand(
        mssqlEntity: Rprij,
        supplierId: UUID,
        productComponentId: UUID,
    ) = CreateOrUpdateStockMovementCommand(
        originalId = mssqlEntity.rprijid,
        supplierId = supplierId,
        productComponentId = productComponentId,
        type = StockMovementType.GOODS_RECEIPT,
        quantity = mssqlEntity.mnoz.toBigDecimal(),
        price = mssqlEntity.cenadod.toBigDecimal(),
        receiptNumber = mssqlEntity.doklad.trimIfNotBlank(),
        note = mssqlEntity.pozn.trimIfNotBlank(),
        recordedAt = mssqlEntity.zaloz
    )
}
