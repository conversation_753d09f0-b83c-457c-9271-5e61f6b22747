package com.cleevio.cinemax.api.module.ticket.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus
import java.util.UUID

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class TicketsNotFoundException(ticketIds: Set<UUID>) : ApiException(
    Module.TICKET,
    TicketErrorType.TICKETS_NOT_FOUND
) {
    override val message: String = ticketIds.joinToString(", ")
}
