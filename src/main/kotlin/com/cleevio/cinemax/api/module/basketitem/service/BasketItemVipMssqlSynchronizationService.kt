package com.cleevio.cinemax.api.module.basketitem.service

import com.cleevio.cinemax.api.common.constant.BASKET_ITEM
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.basket.service.BasketFinderService
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemLockValues.SYNCHRONIZE_CREATED_VIP_FROM_MSSQL
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemLockValues.SYNCHRONIZE_DELETED_VIP_FROM_MSSQL
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemLockValues.SYNCHRONIZE_UPDATED_VIP_FROM_MSSQL
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateVipBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.PatchBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.util.parseProdmenuQuantity
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.table.constant.TableType
import com.cleevio.cinemax.api.module.table.entity.Table
import com.cleevio.cinemax.api.module.table.service.TableFinderService
import com.cleevio.cinemax.api.module.table.service.TableMssqlFinderRepository
import com.cleevio.cinemax.api.module.table.util.mapPaymentType
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class BasketItemVipMssqlSynchronizationService(
    private val basketItemVipService: BasketItemVipService,
    private val basketItemJooqFinderRepository: BasketItemJooqFinderRepository,
    private val basketItemMssqlFinderRepository: BasketItemMssqlFinderRepository,
    private val tableMssqlFinderRepository: TableMssqlFinderRepository,
    private val tableFinderService: TableFinderService,
    private val basketFinderService: BasketFinderService,
    private val basketItemService: BasketItemService,
) {
    private val log = logger()

    @TryLock(BASKET_ITEM, SYNCHRONIZE_CREATED_VIP_FROM_MSSQL)
    fun synchronizeCreatedVipBasketItems() {
        log.debug("Starting synchronization of basket items on VIP tables from MSSQL.")

        val vipSeats = fetchVipSeats()
        val vipSeatOriginalIds = vipSeats.mapToSet { it.originalId }
        val allVipMssqlBasketItems = basketItemMssqlFinderRepository.findAllByTableOriginalIdIn(vipSeatOriginalIds)
        val processedVipMssqlItemOriginalIds = basketItemJooqFinderRepository.findNonDeletedByOriginalIdInAndBasketIsOpen(
            allVipMssqlBasketItems.mapToSet { it.prodmenuid }
        ).mapToSet { it.originalId }

        val unprocessedVipMssqlBasketItems = allVipMssqlBasketItems
            .filter { !processedVipMssqlItemOriginalIds.contains(it.prodmenuid) }
            .also {
                if (it.isEmpty()) {
                    log.debug("No unprocessed basket items on VIP tables to synchronize found, skipping execution.")
                    return
                }
            }
        log.info("Found ${unprocessedVipMssqlBasketItems.size} unprocessed basket items on VIP tables to synchronize.")

        val vipSeatsOriginalIdToId = vipSeats.associate { it.originalId to it.id }
        val mssqlVipSeatsOriginalIdToPaymentType = tableMssqlFinderRepository.findAllVipSeats()
            .associate { it.stolyid to mapPaymentType(it) }

        unprocessedVipMssqlBasketItems.forEach {
            basketItemVipService.createVipBasketItem(
                CreateVipBasketItemCommand(
                    mssqlBasketItem = it,
                    tableId = vipSeatsOriginalIdToId[it.stolyid]!!,
                    preferredPaymentType = mssqlVipSeatsOriginalIdToPaymentType[it.stolyid]!!
                )
            )
        }
        log.debug("Synchronization of basket items on VIP tables from MSSQL finished.")
    }

    @TryLock(BASKET_ITEM, SYNCHRONIZE_DELETED_VIP_FROM_MSSQL)
    fun synchronizeDeletedVipBasketItems() {
        log.debug("Starting synchronization of deleted basket items on VIP tables from MSSQL.")

        val vipSeats = fetchVipSeats()
        val vipSeatIds = vipSeats.mapToSet { it.id }
        val vipSeatOriginalIds = vipSeats.mapToSet { it.originalId }

        val vipPosBasketIds = basketFinderService.findNonDeletedByTableIdInAndStateIsOpen(vipSeatIds).mapToSet { it.id }
        val vipPosBasketItems = basketItemJooqFinderRepository.findAllNonDeletedWithOriginalIdByBasketIdIn(vipPosBasketIds)
            .also {
                if (it.isEmpty()) {
                    log.debug("No basket items on VIP tables found in POS, skipping execution.")
                    return
                }
            }

        val vipPosBasketItemOriginalIdToBasketItem = vipPosBasketItems.associateBy { it.originalId }
        val vipPosBasketItemOriginalIds = vipPosBasketItems.mapToSet { it.originalId }
        val vipMssqlBasketItemOriginalIds = basketItemMssqlFinderRepository.findAllByTableOriginalIdIn(vipSeatOriginalIds)
            .mapToSet { it.prodmenuid }
        val posBasketItemToDeleteOriginalIds = vipPosBasketItemOriginalIds subtract vipMssqlBasketItemOriginalIds
        log.info("Found ${posBasketItemToDeleteOriginalIds.size} basket items on VIP tables in POS to delete.")

        posBasketItemToDeleteOriginalIds.forEach {
            val basketItemToDelete = vipPosBasketItemOriginalIdToBasketItem[it]!!
            basketItemService.deleteBasketItem(
                DeleteBasketItemCommand(
                    basketId = basketItemToDelete.basketId,
                    basketItemId = basketItemToDelete.id
                )
            )
        }
        log.debug("Synchronization of deleted basket items on VIP tables from MSSQL finished.")
    }

    @TryLock(BASKET_ITEM, SYNCHRONIZE_UPDATED_VIP_FROM_MSSQL)
    fun synchronizeUpdatedVipBasketItems() {
        log.debug("Starting synchronization of updated basket items on VIP tables from MSSQL.")

        val vipSeats = fetchVipSeats()
        val vipSeatOriginalIds = vipSeats.mapToSet { it.originalId }
        val vipMssqlBasketItems = basketItemMssqlFinderRepository.findAllByTableOriginalIdIn(vipSeatOriginalIds)
        val vipMssqlBasketItemOriginalIds = vipMssqlBasketItems.mapToSet { it.prodmenuid }
        val vipPosBasketItems = basketItemJooqFinderRepository.findAllNonDeletedByOriginalIdIn(vipMssqlBasketItemOriginalIds)
            .also {
                if (it.isEmpty()) {
                    log.debug("No basket items on VIP tables found in POS, skipping execution.")
                    return
                }
            }

        val vipPosBasketItemOriginalIdToBasketItem = vipPosBasketItems.associateBy { it.originalId }
        val vipMssqlBasketItemOriginalIdToQuantity = vipMssqlBasketItems
            .associate { it.prodmenuid to parseProdmenuQuantity(it.pocet) }
        val vipMssqlBasketItemsToSync = vipMssqlBasketItems.filter {
            val posBasketItem = vipPosBasketItemOriginalIdToBasketItem[it.prodmenuid]
            posBasketItem != null && posBasketItem.quantity != vipMssqlBasketItemOriginalIdToQuantity[it.prodmenuid]
        }
        log.info("Found ${vipMssqlBasketItemsToSync.size} basket items on VIP tables in POS to update.")

        vipMssqlBasketItemsToSync.forEach {
            vipPosBasketItemOriginalIdToBasketItem[it.prodmenuid]?.let { posBasketItem ->
                basketItemService.patchBasketItem(
                    PatchBasketItemCommand(
                        basketId = posBasketItem.basketId,
                        basketItemId = posBasketItem.id,
                        quantity = vipMssqlBasketItemOriginalIdToQuantity[it.prodmenuid]
                    )
                )
            }
        }
        log.debug("Synchronization of updated basket items on VIP tables from MSSQL finished.")
    }

    private fun fetchVipSeats(): Set<Table> {
        return tableFinderService.findAllByProductModeAndTableType(
            productMode = ProductMode.VIP,
            tableType = TableType.SEAT
        ).toSet()
    }
}
