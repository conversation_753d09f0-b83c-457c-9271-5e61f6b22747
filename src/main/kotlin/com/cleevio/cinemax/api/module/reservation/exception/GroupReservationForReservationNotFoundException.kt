package com.cleevio.cinemax.api.module.reservation.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class GroupReservationForReservationNotFoundException : ApiException(
    Module.RESERVATION,
    ReservationErrorType.GROUP_RESERVATION_FOR_RESERVATION_NOT_FOUND
)
