package com.cleevio.cinemax.api.module.productcategory.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.module.productcategory.controller.dto.VipProductCategoriesResponse
import com.cleevio.cinemax.api.module.productcategory.service.GetVipProductCategoriesByTableIdQueryService
import com.cleevio.cinemax.api.module.productcategory.service.query.GetVipProductCategoriesByTableIdQuery
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@RestController
@RequestMapping("/vip-app/product-categories")
class VipProductCategoryController(
    private val getVipProductCategoriesByTableIdQueryService: GetVipProductCategoriesByTableIdQueryService,
) {

    @GetMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun getVipProductCategories(
        @RequestParam tabletId: UUID,
    ): VipProductCategoriesResponse {
        return getVipProductCategoriesByTableIdQueryService(GetVipProductCategoriesByTableIdQuery(tabletId))
    }
}
