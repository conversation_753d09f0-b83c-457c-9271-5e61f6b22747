package com.cleevio.cinemax.api.module.dailyclosingmovement.constant

import com.cleevio.cinemax.api.common.constant.ExportLanguage
import com.cleevio.cinemax.api.common.service.ExportableEnum

enum class DailyClosingMovementItemType : ExportableEnum {
    TICKETS {
        override fun getLocalizedNames(): Map<ExportLanguage, String> {
            return mapOf(
                ExportLanguage.CZECH to "Vstupenky",
                ExportLanguage.SLOVAK to "Vstupenky",
                ExportLanguage.ROMANIAN to "Bilete"
            )
        }
    },
    PRODUCTS {
        override fun getLocalizedNames(): Map<ExportLanguage, String> {
            return mapOf(
                ExportLanguage.CZECH to "Bufet",
                ExportLanguage.SLOVAK to "Bufet",
                ExportLanguage.ROMANIAN to "Bufet"
            )
        }
    },
    DEDUCTION {
        override fun getLocalizedNames(): Map<ExportLanguage, String> {
            return mapOf(
                ExportLanguage.CZECH to "Odvod",
                ExportLanguage.SLOVAK to "Odvod",
                ExportLanguage.ROMANIAN to "Priză"
            )
        }
    },
}
