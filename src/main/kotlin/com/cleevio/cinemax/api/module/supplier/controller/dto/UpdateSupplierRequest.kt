package com.cleevio.cinemax.api.module.supplier.controller.dto

import com.cleevio.cinemax.api.module.supplier.service.command.CreateOrUpdateSupplierCommand
import java.util.Optional
import java.util.UUID

data class UpdateSupplierRequest(
    val title: String,
    val addressStreet: Optional<String>?,
    val addressCity: Optional<String>?,
    val addressPostCode: Optional<String>?,
    val contactName: Optional<String>?,
    val contactPhone: Optional<String>?,
    val contactEmails: Optional<Set<String>>?,
    val bankName: Optional<String>?,
    val bankAccount: Optional<String>?,
    val idNumber: Optional<String>?,
    val taxIdNumber: Optional<String>?,
) {

    fun toCommand(supplierId: UUID) = CreateOrUpdateSupplierCommand(
        id = supplierId,
        title = title,
        addressStreet = addressStreet,
        addressCity = addressCity,
        addressPostCode = addressPostCode,
        contactName = contactName,
        contactPhone = contactPhone,
        contactEmails = contactEmails,
        bankName = bankName,
        bankAccount = bankAccount,
        idNumber = idNumber,
        taxIdNumber = taxIdNumber
    )
}
