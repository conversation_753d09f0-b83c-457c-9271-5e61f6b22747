package com.cleevio.cinemax.api.module.basket.service

import com.cleevio.cinemax.api.common.constant.ANONYMOUS_USERNAME
import com.cleevio.cinemax.api.common.constant.BASKET
import com.cleevio.cinemax.api.common.constant.PaymentType
import com.cleevio.cinemax.api.common.util.isLessThan
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.common.util.setAndReturnPrivateProperty
import com.cleevio.cinemax.api.module.basket.constant.BasketLockValues.INIT_BASKET_FOR_TABLE
import com.cleevio.cinemax.api.module.basket.constant.BasketLockValues.UPDATE_OR_DELETE_BASKET
import com.cleevio.cinemax.api.module.basket.constant.BasketState
import com.cleevio.cinemax.api.module.basket.entity.Basket
import com.cleevio.cinemax.api.module.basket.event.BasketPaymentInitiatedEvent
import com.cleevio.cinemax.api.module.basket.event.TableBasketDeletedEvent
import com.cleevio.cinemax.api.module.basket.exception.BasketForTableAlreadyExistsException
import com.cleevio.cinemax.api.module.basket.exception.BasketIsEmptyException
import com.cleevio.cinemax.api.module.basket.exception.TableForBasketNotFoundException
import com.cleevio.cinemax.api.module.basket.service.command.CompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateBasketItemInput
import com.cleevio.cinemax.api.module.basket.service.command.CreateMssqlTicketBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateProductSalesBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.CreateTicketSalesBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.DeleteBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitBasketForTableCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitOnlineBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitOrUpdateMssqlBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.InitiateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.MessagingCreateBasketCommand
import com.cleevio.cinemax.api.module.basket.service.command.MessagingCreateBasketItemCommand
import com.cleevio.cinemax.api.module.basket.service.command.RecalculateBasketTotalPriceCommand
import com.cleevio.cinemax.api.module.basket.service.command.UpdateBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.service.command.WebCompleteBasketPaymentCommand
import com.cleevio.cinemax.api.module.basket.util.PAID_BASKET_STATES
import com.cleevio.cinemax.api.module.basket.util.validateModifiableBasketState
import com.cleevio.cinemax.api.module.basket.util.validateOpenBasketState
import com.cleevio.cinemax.api.module.basket.util.validateOpenOnlineBasketState
import com.cleevio.cinemax.api.module.basket.util.validatePaymentInProgressBasketState
import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateOnlineTicketBasketItemInput
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateOnlineTicketBasketItemsCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateProductSalesBasketItemCommand
import com.cleevio.cinemax.api.module.basketitem.service.command.DeleteBasketItemsCommand
import com.cleevio.cinemax.api.module.branch.service.BranchJpaFinderService
import com.cleevio.cinemax.api.module.discountcard.event.DiscountCardsNotAppliedEvent
import com.cleevio.cinemax.api.module.discountcardusage.entity.hasNoBasketItemRelation
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageFinderService
import com.cleevio.cinemax.api.module.outboxevent.event.WebBasketPaymentCompletedEvent
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJpaFinderService
import com.cleevio.cinemax.api.module.reservation.constant.ReservationState
import com.cleevio.cinemax.api.module.reservation.service.ReservationService
import com.cleevio.cinemax.api.module.reservation.service.command.UpdateReservationStateCommand
import com.cleevio.cinemax.api.module.table.event.BasketPaidEvent
import com.cleevio.cinemax.api.module.table.event.TableBasketInitiatedEvent
import com.cleevio.cinemax.api.module.table.service.TableFinderService
import com.cleevio.cinemax.api.module.ticket.exception.ReservationForTicketNotFoundException
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

@Service
@Validated
class BasketService(
    private val basketRepository: BasketRepository,
    private val basketFinderRepository: BasketFinderRepository,
    private val basketFinderService: BasketFinderService,
    private val basketItemService: BasketItemService,
    private val basketItemJpaFinderService: BasketItemJpaFinderService,
    private val tableFinderService: TableFinderService,
    private val reservationService: ReservationService,
    private val ticketJooqFinderService: TicketJooqFinderService,
    private val discountCardUsageFinderService: DiscountCardUsageFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
    private val posConfigurationJpaFinderService: PosConfigurationJpaFinderService,
    private val branchJpaFinderService: BranchJpaFinderService,
) {
    private val log = logger()

    @Transactional
    fun initBasket(@Valid command: InitBasketCommand): Basket {
        val createdBasket = basketRepository.saveAndFlush(
            Basket(
                totalPrice = 0.toBigDecimal(),
                state = BasketState.OPEN
            )
        )
        createBasketItems(command.items, createdBasket)

        return recalculateBasketTotalPrice(
            command = RecalculateBasketTotalPriceCommand(createdBasket.id)
        )
    }

    @Transactional
    fun initOnlineBasket(@Valid command: InitOnlineBasketCommand): Basket {
        val onlinePosConfigurationId = posConfigurationJpaFinderService.getOnlinePosConfigurationId()

        val createdBasket = basketRepository.saveAndFlush(
            Basket(
                totalPrice = 0.toBigDecimal(),
                state = BasketState.OPEN_ONLINE,
                paymentType = PaymentType.CASHLESS,
                paymentPosConfigurationId = onlinePosConfigurationId
            )
        )

        // with basked items also tickets going to be created
        basketItemService.createOnlineTicketBasketItems(
            CreateOnlineTicketBasketItemsCommand(
                basketId = createdBasket.id,
                // screening is same for all tickets
                screeningId = command.tickets.first().screeningId,
                onlineTicketBasketItems = command.tickets.map {
                    CreateOnlineTicketBasketItemInput(
                        priceCategoryItemNumber = it.priceCategoryItemNumber,
                        discountCardId = it.discountCardId,
                        primaryTicketDiscountId = it.ticketDiscountId,
                        seatId = it.seatId,
                        reservationId = it.reservationId
                    )
                }.toSet()
            )
        )

        return recalculateBasketTotalPrice(
            command = RecalculateBasketTotalPriceCommand(createdBasket.id)
        )
    }

    @Transactional
    @Lock(BASKET, INIT_BASKET_FOR_TABLE)
    fun initBasketForTable(
        @Valid
        @LockFieldParameter("tableId")
        command: InitBasketForTableCommand,
    ): Basket {
        tableFinderService.findById(command.tableId) ?: throw TableForBasketNotFoundException()
        if (basketFinderRepository.existsByTableIdAndStateIsOpen(command.tableId)) {
            throw BasketForTableAlreadyExistsException()
        }

        val createdBasket = basketRepository.saveAndFlush(
            Basket(
                tableId = command.tableId,
                totalPrice = 0.toBigDecimal(),
                state = BasketState.OPEN,
                preferredPaymentType = command.preferredPaymentType
            )
        )
        createBasketItems(command.items, createdBasket)

        applicationEventPublisher.publishEvent(
            TableBasketInitiatedEvent(
                basketId = createdBasket.id,
                tableId = command.tableId,
                initiatedInNewPos = command.initiatedInNewPos
            )
        )

        return recalculateBasketTotalPrice(
            command = RecalculateBasketTotalPriceCommand(createdBasket.id)
        )
    }

    @Transactional
    @Lock(BASKET, UPDATE_OR_DELETE_BASKET)
    fun initOrUpdateMssqlBasket(
        @Valid
        @LockFieldParameter("originalId")
        command: InitOrUpdateMssqlBasketCommand,
    ) {
        require(command.variableSymbol != null || command.paymentPosConfigurationId != null) {
            "Either variableSymbol or paymentPosConfigurationId must be specified."
        }

        basketItemJpaFinderService.findNonCancelledNonDeletedByTicketReceiptNumber(command.receiptNumber)?.let { existingItem ->
            if (command.deleteAndCreate) {
                // items eligible for delete and create are hard deleted and recreated later
                basketItemService.hardDeleteTicketBasketItem(existingItem.id)
            } else {
                // legacy cancellation items are not checked for receiptNumber duplicates
                if (!command.isCancellationItem) {
                    log.info("BasketItem with receiptNumber ${command.receiptNumber} already exists, skipping.")
                    return
                }
            }
        }

        // either find existing online basket by variable symbol, offline basket by POS and date, or create a new basket
        val basket = if (command.variableSymbol == null) {
            basketRepository.findByPaidAtAndPaymentPosConfigurationId(
                paidAt = command.paidAt,
                paymentPosConfigurationId = command.paymentPosConfigurationId!!
            )
        } else {
            basketRepository.findByVariableSymbol(command.variableSymbol)
        } ?: run {
            basketRepository.saveAndFlush(
                Basket(
                    totalPrice = 0.toBigDecimal(),
                    state = command.variableSymbol?.let { BasketState.PAID_ONLINE } ?: BasketState.PAID,
                    paymentType = command.paymentType,
                    paidAt = command.paidAt,
                    paymentPosConfigurationId = command.paymentPosConfigurationId,
                    variableSymbol = command.variableSymbol
                ).also {
                    it.setAndReturnPrivateProperty("createdBy", command.posTitle ?: ANONYMOUS_USERNAME)
                    it.setAndReturnPrivateProperty("updatedBy", command.posTitle ?: ANONYMOUS_USERNAME)
                }
            )
        }

        basketItemService.createMssqlTicketBasketItem(
            CreateMssqlTicketBasketItemCommand(
                basketId = basket.id,
                screeningId = command.screeningId,
                reservationId = command.reservationId,
                originalId = command.originalId,
                seatId = command.seatId,
                priceCategoryItemNumber = command.priceCategoryItemNumber,
                receiptNumber = command.receiptNumber,
                ticketDiscountPrimaryId = command.ticketDiscountPrimaryId,
                ticketDiscountSecondaryId = command.ticketDiscountSecondaryId,
                discountCardId = command.discountCardId,
                isUsed = command.isUsed,
                includes3dGlasses = command.includes3dGlasses,
                branchId = branchJpaFinderService.getCurrentBranch().id,
                basePrice = command.basePrice,
                basePriceBeforeDiscount = command.basePriceBeforeDiscount,
                seatSurcharge = command.seatSurcharge,
                seatSurchargeType = command.seatSurchargeType,
                auditoriumSurcharge = command.auditoriumSurcharge,
                auditoriumSurchargeType = command.auditoriumSurchargeType,
                seatServiceFee = command.seatServiceFee,
                seatServiceFeeType = command.seatServiceFeeType,
                auditoriumServiceFee = command.auditoriumServiceFee,
                auditoriumServiceFeeType = command.auditoriumServiceFeeType,
                serviceFeeGeneral = command.serviceFeeGeneral,
                freeTicket = command.freeTicket,
                totalPrice = command.totalPrice
            )
        )

        recalculateBasketTotalPrice(
            command = RecalculateBasketTotalPriceCommand(basket.id)
        )
    }

    @Transactional
    fun createProductSalesBaskets(commands: List<@Valid CreateProductSalesBasketCommand>) {
        val productSaleCommandLists = commands.groupBy { it.receiptNumber }.values.toList()

        productSaleCommandLists.forEach {
            val basket = basketRepository.saveAndFlush(
                Basket(
                    totalPrice = 0.toBigDecimal(),
                    state = BasketState.PAID,
                    paymentType = it.first().paymentType,
                    paidAt = it.first().paidAt
                )
            )

            it.forEach { command ->
                basketItemService.createProductSalesBasketItem(
                    CreateProductSalesBasketItemCommand(
                        basketId = basket.id,
                        productId = command.productId,
                        type = command.type,
                        price = command.price,
                        quantity = command.quantity,
                        receiptNumber = command.receiptNumber,
                        isCancelled = command.isCancelled,
                        branchId = command.branchId
                    )
                )
            }

            recalculateBasketTotalPrice(
                command = RecalculateBasketTotalPriceCommand(basket.id)
            )
        }
    }

    @Transactional
    fun createTicketSalesBaskets(commands: List<@Valid CreateTicketSalesBasketCommand>) {
        val ticketSaleCommandLists = commands.groupBy {
            TicketSalesBasketKey(it.paymentPosConfigurationId, it.paidAt)
        }

        ticketSaleCommandLists.forEach { entry ->
            val basketCommands = entry.value
            val firstCommand = basketCommands.first()

            // either find existing online basket by variable symbol, offline basket by POS and date, or create a new basket
            val basket = if (firstCommand.variableSymbol == null) {
                basketRepository.findByPaidAtAndPaymentPosConfigurationId(
                    paidAt = firstCommand.paidAt,
                    paymentPosConfigurationId = firstCommand.paymentPosConfigurationId
                )
            } else {
                basketRepository.findByVariableSymbol(firstCommand.variableSymbol)
            } ?: run {
                basketRepository.saveAndFlush(
                    Basket(
                        totalPrice = 0.toBigDecimal(),
                        state = firstCommand.state,
                        paymentType = firstCommand.paymentType,
                        paymentPosConfigurationId = firstCommand.paymentPosConfigurationId,
                        paidAt = firstCommand.paidAt,
                        variableSymbol = firstCommand.variableSymbol
                    )
                )
            }

            basketCommands.forEach { command ->
                // TODO include cancellation in this logic
                // if (ticketJooqFinderService.existsNonDeletedByReceiptNumber(command.receiptNumber)) {
                //     log.info("Ticket receiptNumber=${command.receiptNumber} already exists, skipping.")
                //     return@forEach
                // }

                basketItemService.createMssqlTicketBasketItem(
                    CreateMssqlTicketBasketItemCommand(
                        basketId = basket.id,
                        screeningId = command.screeningId,
                        // reservation is created later in TicketService
                        reservationId = null,
                        originalId = command.originalId,
                        seatId = command.seatId,
                        priceCategoryItemNumber = command.priceCategoryItemNumber,
                        receiptNumber = command.receiptNumber,
                        ticketDiscountPrimaryId = command.ticketDiscountPrimaryId,
                        ticketDiscountSecondaryId = command.ticketDiscountSecondaryId,
                        discountCardId = command.discountCardId,
                        isUsed = command.isUsed,
                        includes3dGlasses = command.includes3dGlasses,
                        branchId = command.branchId,
                        basePrice = command.basePrice,
                        basePriceBeforeDiscount = command.basePriceBeforeDiscount,
                        seatSurcharge = command.seatSurcharge,
                        seatSurchargeType = command.seatSurchargeType,
                        auditoriumSurcharge = command.auditoriumSurcharge,
                        auditoriumSurchargeType = command.auditoriumSurchargeType,
                        seatServiceFee = command.seatServiceFee,
                        seatServiceFeeType = command.seatServiceFeeType,
                        auditoriumServiceFee = command.auditoriumServiceFee,
                        auditoriumServiceFeeType = command.auditoriumServiceFeeType,
                        serviceFeeGeneral = command.serviceFeeGeneral,
                        freeTicket = command.freeTicket,
                        totalPrice = command.totalPrice
                    )
                )
            }

            recalculateBasketTotalPrice(
                command = RecalculateBasketTotalPriceCommand(basket.id)
            )
        }
    }

    @Transactional
    @Lock(BASKET, UPDATE_OR_DELETE_BASKET)
    fun messagingCreateBasket(
        @Valid
        @LockFieldParameter("id") command: MessagingCreateBasketCommand,
    ) {
        if (basketFinderService.existsById(command.id)) {
            log.warn("Attempt to create duplicate messaging Basket ${command.id}. Skipping.")
            return
        }
        if (command.state !in PAID_BASKET_STATES) {
            error("Can't create messaging Basket ${command.id} with state=${command.state}.")
        }

        basketRepository.saveAndFlush(
            Basket(
                id = command.id,
                totalPrice = command.totalPrice,
                state = command.state,
                paymentType = command.paymentType,
                paidAt = command.paidAt,
                variableSymbol = command.variableSymbol
            )
        )

        command.items.forEach { itemCommand ->
            basketItemService.messagingCreateBasketItem(
                MessagingCreateBasketItemCommand(
                    id = itemCommand.id,
                    basketId = command.id,
                    type = itemCommand.type,
                    price = itemCommand.price,
                    quantity = itemCommand.quantity,
                    isCancelled = itemCommand.isCancelled,
                    discountCardCode = itemCommand.discountCardCode,
                    branchCode = command.branchCode,
                    product = itemCommand.product?.let { product ->
                        MessagingCreateBasketItemCommand.BasketProductItem(
                            productCode = product.productCode,
                            productReceiptNumber = product.productReceiptNumber
                        )
                    },
                    ticket = itemCommand.ticket?.let { ticket ->
                        MessagingCreateBasketItemCommand.BasketTicketItem(
                            id = ticket.id,
                            screeningId = ticket.screeningId,
                            auditoriumOriginalCode = ticket.auditoriumOriginalCode,
                            seatOriginalId = ticket.seatOriginalId,
                            basePriceItemNumber = ticket.basePriceItemNumber,
                            ticketReceiptNumber = ticket.ticketReceiptNumber,
                            ticketDiscountPrimaryCode = ticket.ticketDiscountPrimaryCode,
                            ticketDiscountSecondaryCode = ticket.ticketDiscountSecondaryCode,
                            isGroupTicket = ticket.isGroupTicket,
                            isUsed = ticket.isUsed,
                            includes3dGlasses = ticket.includes3dGlasses
                        )
                    }
                )
            )
        }
    }

    @Transactional
    @Lock(BASKET, UPDATE_OR_DELETE_BASKET)
    fun deleteBasket(
        @Valid
        @LockFieldParameter("basketId")
        command: DeleteBasketCommand,
    ) {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }
        val basketItemIds = basketItemJpaFinderService.findAllNonDeletedByBasketId(command.basketId).mapToSet { it.id }

        basketItemService.deleteBasketItems(
            DeleteBasketItemsCommand(
                basketId = basket.id,
                basketItemIds = basketItemIds
            )
        )
        deleteDiscountCardUsages(basketId = basket.id)

        basket.tableId?.let {
            tableFinderService.findById(it) ?: throw TableForBasketNotFoundException()
            applicationEventPublisher.publishEvent(
                TableBasketDeletedEvent(
                    basketId = basket.id
                )
            )
        }

        basketRepository.save(
            basket.apply {
                state = BasketState.DELETED
                markDeleted()
            }
        )
    }

    @Transactional
    @Lock(BASKET, UPDATE_OR_DELETE_BASKET)
    fun initiateBasketPayment(
        @Valid
        @LockFieldParameter("basketId")
        command: InitiateBasketPaymentCommand,
    ): Basket {
        val basket = basketFinderService.getNonDeletedById(command.basketId).also {
            validateOpenBasketState(it)
        }

        basketItemJpaFinderService.findAllNonDeletedByBasketId(command.basketId).also {
            if (it.isEmpty()) throw BasketIsEmptyException()
        }.also {
            updateBasketItemReservations(basketItems = it)
            deleteDiscountCardUsages(basketId = basket.id, trailingUsagesOnly = true)
        }

        applicationEventPublisher.publishEvent(BasketPaymentInitiatedEvent(basket.id))

        return basketRepository.save(
            basket.apply {
                state = BasketState.PAYMENT_IN_PROGRESS
                paymentType = command.paymentType
                paymentPosConfigurationId = command.posConfigurationId
            }
        )
    }

    @Transactional
    @Lock(BASKET, UPDATE_OR_DELETE_BASKET)
    fun updateBasketPayment(
        @Valid
        @LockFieldParameter("basketId")
        command: UpdateBasketPaymentCommand,
    ): Basket {
        return basketFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }.also {
            basketRepository.save(
                it.apply {
                    paymentType = command.paymentType
                }
            )
        }
    }

    @Transactional
    @Lock(BASKET, UPDATE_OR_DELETE_BASKET)
    fun completeBasketPayment(
        @Valid
        @LockFieldParameter("basketId")
        command: CompleteBasketPaymentCommand,
    ): Basket {
        return basketFinderService.getNonDeletedById(command.basketId).also {
            validatePaymentInProgressBasketState(it)
            basketRepository.save(
                it.apply {
                    state = BasketState.PAID
                    paidAt = LocalDateTime.now()
                }
            )
            applicationEventPublisher.publishEvent(
                BasketPaidEvent(
                    basketId = it.id,
                    tableId = it.tableId
                )
            )
        }
    }

    @Transactional
    @Lock(BASKET, UPDATE_OR_DELETE_BASKET)
    fun webCompleteWebBasketPayment(
        @Valid
        @LockFieldParameter("basketId")
        command: WebCompleteBasketPaymentCommand,
    ) {
        basketFinderService.getNonDeletedById(command.basketId).also {
            validateOpenOnlineBasketState(it)
            basketRepository.save(
                it.apply {
                    state = BasketState.PAID_ONLINE
                    paidAt = LocalDateTime.now()
                    paymentPosConfigurationId = posConfigurationJpaFinderService.getOnlinePosConfigurationId()
                    variableSymbol = command.variableSymbol
                }
            )
            applicationEventPublisher.publishEvent(
                BasketPaidEvent(
                    basketId = it.id,
                    tableId = it.tableId
                )
            )
            applicationEventPublisher.publishEvent(WebBasketPaymentCompletedEvent(it.id))
        }
    }

    @Transactional
    @Lock(BASKET, UPDATE_OR_DELETE_BASKET)
    fun recalculateBasketTotalPrice(
        @Valid
        @LockFieldParameter("basketId")
        command: RecalculateBasketTotalPriceCommand,
    ): Basket {
        val basket = basketRepository.findById(command.basketId).get()
        val basketItems = basketItemJpaFinderService.findAllNonDeletedByBasketId(command.basketId)
        val ticketsTotalPrice = basketItems.filter { it.type == BasketItemType.TICKET }.sumOf { it.price }
        val nonIsolatedProductsTotalPrice =
            basketItems.filter { it.type == BasketItemType.PRODUCT && !it.belongsToIsolatedGroup() }.sumOf { it.price }
        val isolatedProductsTotalPrice =
            basketItems.filter { it.type == BasketItemType.PRODUCT && it.belongsToIsolatedGroup() }.sumOf { it.price }

        val productDiscountBasketItems = basketItems.filter { it.type == BasketItemType.PRODUCT_DISCOUNT }
        val highestNonIsolatedProductDiscountPrice = productDiscountBasketItems
            .filter { !it.belongsToIsolatedGroup() }
            .maxByOrNull { it.price.negate() }?.price ?: 0.toBigDecimal()
        val isolatedProductDiscountTotalPrice = productDiscountBasketItems
            .filter { it.belongsToIsolatedGroup() }
            .sumOf { it.price }

        return basketRepository.save(
            basket.apply {
                totalPrice = sumAllTotalPriceParts(
                    ticketsTotalPrice = ticketsTotalPrice,
                    nonIsolatedProductsTotalPrice = nonIsolatedProductsTotalPrice,
                    highestNonIsolatedProductDiscountPrice = highestNonIsolatedProductDiscountPrice,
                    isolatedProductsTotalPrice = isolatedProductsTotalPrice,
                    isolatedProductDiscountTotalPrice = isolatedProductDiscountTotalPrice
                )
            }
        )
    }

    private fun updateBasketItemReservations(basketItems: List<BasketItem>) {
        val ticketIds = basketItems.mapNotNull { it.ticketId }.toSet()
        val tickets = ticketJooqFinderService.findAllNonDeletedByIdIn(ticketIds)
        val ticketIdToReservationId = tickets.associate { it.id to it.reservationId }

        basketItems.forEach {
            it.ticketId?.let { ticketId ->
                reservationService.updateReservationState(
                    UpdateReservationStateCommand(
                        reservationId = ticketIdToReservationId[ticketId]
                            ?: throw ReservationForTicketNotFoundException(),
                        state = ReservationState.UNAVAILABLE
                    )
                )
            }
        }
    }

    private fun deleteDiscountCardUsages(basketId: UUID, trailingUsagesOnly: Boolean = false) {
        val usagesToDeleteIds = discountCardUsageFinderService.findAllNonDeletedByBasketId(basketId)
            .filter { if (trailingUsagesOnly) it.hasNoBasketItemRelation() else true }
            .mapToSet { it.id }
        if (usagesToDeleteIds.isNotEmpty()) {
            applicationEventPublisher.publishEvent(DiscountCardsNotAppliedEvent(usagesToDeleteIds))
        }
    }

    private fun createBasketItems(itemInputs: List<CreateBasketItemInput>, createdBasket: Basket) {
        itemInputs.forEach { request ->
            CreateBasketItemCommand(
                basketId = createdBasket.id,
                type = request.type,
                quantity = request.quantity,
                screeningId = request.ticket?.screeningId,
                reservationSeatId = request.ticket?.reservation?.seatId,
                productId = request.product?.productId,
                productIsolatedWithId = request.product?.productIsolatedWithId,
                priceCategoryItemNumber = request.ticket?.ticketPrice?.priceCategoryItemNumber,
                primaryTicketDiscountId = request.ticket?.primaryDiscount?.ticketDiscountId,
                secondaryTicketDiscountId = request.ticket?.secondaryDiscount?.ticketDiscountId,
                primaryTicketDiscountCardId = request.ticket?.primaryDiscount?.discountCardId,
                secondaryTicketDiscountCardId = request.ticket?.secondaryDiscount?.discountCardId,
                productDiscountCardId = request.product?.discountCardId,
                isGroupTicket = request.ticket?.isGroupTicket
            ).also {
                basketItemService.createBasketItem(it)
            }
        }
    }

    private fun sumAllTotalPriceParts(
        ticketsTotalPrice: BigDecimal,
        nonIsolatedProductsTotalPrice: BigDecimal,
        highestNonIsolatedProductDiscountPrice: BigDecimal,
        isolatedProductsTotalPrice: BigDecimal,
        isolatedProductDiscountTotalPrice: BigDecimal,
    ): BigDecimal {
        val nonIsolatedTotalPrice =
            if (highestNonIsolatedProductDiscountPrice.negate() > nonIsolatedProductsTotalPrice) {
                0.toBigDecimal()
            } else {
                nonIsolatedProductsTotalPrice + highestNonIsolatedProductDiscountPrice
            }

        (ticketsTotalPrice + isolatedProductsTotalPrice + isolatedProductDiscountTotalPrice + nonIsolatedTotalPrice).also {
            return if (it.isLessThan(0.toBigDecimal())) 0.toBigDecimal() else it
        }
    }

    private data class TicketSalesBasketKey(
        val posConfigurationId: UUID,
        val paidAt: LocalDateTime,
    )
}
