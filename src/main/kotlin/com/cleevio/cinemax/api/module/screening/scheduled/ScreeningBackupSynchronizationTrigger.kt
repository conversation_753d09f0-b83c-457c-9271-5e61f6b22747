package com.cleevio.cinemax.api.module.screening.scheduled

import com.cleevio.cinemax.api.module.screening.service.ScreeningMssqlSynchronizationService
import org.springframework.scheduling.annotation.Scheduled
import org.springframework.stereotype.Component

@Component
class ScreeningBackupSynchronizationTrigger(
    private val screeningMssqlSynchronizationService: ScreeningMssqlSynchronizationService,
) {

    @Scheduled(cron = "\${features.screening.synchronize-backup.cron}")
    fun synchronizeAllUpdatedSinceLastWeek() {
        screeningMssqlSynchronizationService.synchronizeAllUpdatedSinceLastWeek()
    }
}
