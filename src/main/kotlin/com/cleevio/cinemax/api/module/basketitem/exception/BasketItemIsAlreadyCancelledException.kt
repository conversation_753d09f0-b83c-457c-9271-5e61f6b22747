package com.cleevio.cinemax.api.module.basketitem.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.CONFLICT)
class BasketItemIsAlreadyCancelledException : ApiException(
    Module.BASKET_ITEM,
    BasketItemErrorType.BASKET_ITEM_IS_ALREADY_CANCELLED
)
