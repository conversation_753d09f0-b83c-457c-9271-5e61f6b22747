package com.cleevio.cinemax.api.module.supplier.service

import com.cleevio.cinemax.api.common.util.buildUnaccentLikeIgnoreCaseCondition
import com.cleevio.cinemax.api.common.util.paginationAndSorting
import com.cleevio.cinemax.api.module.supplier.controller.dto.AdminSearchSuppliersResponse
import com.cleevio.cinemax.api.module.supplier.service.query.AdminSearchSuppliersQuery
import com.cleevio.cinemax.psql.Tables.SUPPLIER
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated

@Service
@Validated
class AdminSearchSuppliersQueryService(
    private val psqlDslContext: DSLContext,
) {

    operator fun invoke(
        @Valid query: AdminSearchSuppliersQuery,
    ): Page<AdminSearchSuppliersResponse> {
        val conditions = listOfNotNull(
            SUPPLIER.DELETED_AT.isNull,
            buildUnaccentLikeIgnoreCaseCondition(query.filter.title, SUPPLIER.TITLE)
        )

        val count = psqlDslContext.fetchCount(SUPPLIER, conditions)

        return psqlDslContext
            .selectFrom(SUPPLIER)
            .where(conditions)
            .paginationAndSorting(query.pageable, SUPPLIER)
            .fetch()
            .map {
                AdminSearchSuppliersResponse(
                    id = it[SUPPLIER.ID],
                    code = it[SUPPLIER.CODE],
                    title = it[SUPPLIER.TITLE],
                    createdAt = it[SUPPLIER.CREATED_AT],
                    updatedAt = it[SUPPLIER.UPDATED_AT]
                )
            }.let {
                PageImpl(it, query.pageable, count.toLong())
            }
    }
}
