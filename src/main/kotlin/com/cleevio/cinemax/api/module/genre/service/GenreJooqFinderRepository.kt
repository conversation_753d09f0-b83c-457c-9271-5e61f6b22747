package com.cleevio.cinemax.api.module.genre.service

import com.cleevio.cinemax.api.common.service.JooqFinderRepository
import com.cleevio.cinemax.api.module.genre.entity.Genre
import com.cleevio.cinemax.psql.Tables.GENRE
import org.jooq.DSLContext
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
class GenreJooqFinderRepository(
    private val psqlDslContext: DSLContext,
) : JooqFinderRepository<Genre> {

    override fun findAll(): List<Genre> {
        return psqlDslContext
            .selectFrom(GENRE)
            .fetchInto(Genre::class.java)
    }

    override fun findById(id: UUID): Genre? {
        return psqlDslContext
            .selectFrom(GENRE)
            .where(GENRE.ID.eq(id))
            .fetchOneInto(Genre::class.java)
    }

    fun findByOriginalId(originalId: Int): Genre? {
        return psqlDslContext
            .selectFrom(GENRE)
            .where(GENRE.ORIGINAL_ID.eq(originalId))
            .fetchOneInto(Genre::class.java)
    }
}
