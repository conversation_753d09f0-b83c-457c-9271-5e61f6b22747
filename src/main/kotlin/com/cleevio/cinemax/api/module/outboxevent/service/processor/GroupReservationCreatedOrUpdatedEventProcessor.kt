package com.cleevio.cinemax.api.module.outboxevent.service.processor

import com.cleevio.cinemax.api.common.util.MAPPER
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.readValue
import com.cleevio.cinemax.api.module.groupreservation.entity.GroupReservation
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationJpaFinderService
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationMssqlFinderRepository
import com.cleevio.cinemax.api.module.groupreservation.service.GroupReservationService
import com.cleevio.cinemax.api.module.groupreservation.service.command.UpdateGroupReservationOriginalIdCommand
import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.outboxevent.event.GroupReservationSyncedEvent
import com.cleevio.cinemax.api.module.outboxevent.model.GroupReservationCreatedEventData
import com.cleevio.cinemax.api.module.screening.entity.Screening
import com.cleevio.cinemax.api.module.screening.service.ScreeningJpaFinderService
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.REZERVACE
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.records.RezervaceRecord
import org.jooq.DSLContext
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import java.time.LocalDateTime

@Service
class GroupReservationCreatedOrUpdatedEventProcessor(
    private val mssqlCinemaxDslContext: DSLContext,
    private val groupReservationJpaFinderService: GroupReservationJpaFinderService,
    private val groupReservationMssqlFinderRepository: GroupReservationMssqlFinderRepository,
    private val groupReservationService: GroupReservationService,
    private val screeningJpaFinderService: ScreeningJpaFinderService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val log = logger()

    @Transactional(transactionManager = "mssqlCinemaxTransactionManager")
    fun process(event: OutboxEvent): Int {
        log.info("Starting to process OutboxEvent=$event.")

        groupReservationJpaFinderService.findNonDeletedById(event.entityId)?.let {
            val eventData: GroupReservationCreatedEventData = MAPPER.readValue(event.data)
            val screening = screeningJpaFinderService.getNonDeletedById(eventData.screeningId)

            it.originalId?.let { originalId ->
                if (!groupReservationMssqlFinderRepository.existsByOriginalId(originalId)) {
                    log.error("MSSQL rezervace record with originalId $originalId not found.")
                    return 0
                }
                updateMssqlGroupReservationRecord(it, screening, eventData.seatIds.size)?.let { _ ->
                    applicationEventPublisher.publishEvent(
                        GroupReservationSyncedEvent(
                            groupReservationId = it.id,
                            screeningId = screening.id,
                            seatIds = eventData.seatIds
                        )
                    )
                    return 1
                } ?: run {
                    log.error("MSSQL rezervace record originalId=$originalId was not updated.")
                    return 0
                }
            } ?: run {
                createMssqlGroupReservationRecord(it, screening, eventData.seatIds.size)?.let { originalId ->
                    groupReservationService.updateGroupReservationOriginalId(
                        UpdateGroupReservationOriginalIdCommand(
                            groupReservationId = it.id,
                            originalId = originalId
                        )
                    )
                    applicationEventPublisher.publishEvent(
                        GroupReservationSyncedEvent(
                            groupReservationId = it.id,
                            screeningId = screening.id,
                            seatIds = eventData.seatIds
                        )
                    )
                    return 1
                } ?: run {
                    log.error("MSSQL rezervace record was not created or its originalId could not be fetched.")
                    return 0
                }
            }
        } ?: run {
            log.error("Group reservation with id=${event.entityId} not found.")
            return 0
        }
    }

    private fun createMssqlGroupReservationRecord(
        groupReservation: GroupReservation,
        screening: Screening,
        reservationCount: Int,
    ): Int? {
        return prepareMssqlGroupReservationRecord(groupReservation, screening, reservationCount).let {
            mssqlCinemaxDslContext.insertInto(REZERVACE)
                .set(it)
                .execute().also { result ->
                    if (result == 0) return null
                }

            groupReservationMssqlFinderRepository.findLatestByScreeningOriginalId(screening.originalId!!)?.rezervaceid
        }
    }

    private fun updateMssqlGroupReservationRecord(
        groupReservation: GroupReservation,
        screening: Screening,
        reservationCount: Int,
    ): Int? = prepareMssqlGroupReservationRecord(groupReservation, screening, reservationCount).let {
        mssqlCinemaxDslContext.update(REZERVACE)
            .set(it)
            .where(REZERVACE.REZERVACEID.eq(groupReservation.originalId))
            .execute().also { result ->
                if (result == 0) return null
            }
    }

    private fun prepareMssqlGroupReservationRecord(
        groupReservation: GroupReservation,
        screening: Screening,
        reservationCount: Int,
    ): RezervaceRecord = mssqlCinemaxDslContext.newRecord(REZERVACE).also {
        it.rprogid = screening.originalId
        it.datrez = groupReservation.createdAt
        it.porad = screening.getScreeningTime()
        it.jmeno = groupReservation.name
        it.pocet = reservationCount.toShort()
        it.zuziv = groupReservation.createdBy
        it.zcas = LocalDateTime.now()

        // unused columns need to be set with default values
        it.dokdy = null
        it.popis = ""
        it.okula = 0.toBigDecimal()
    }
}
