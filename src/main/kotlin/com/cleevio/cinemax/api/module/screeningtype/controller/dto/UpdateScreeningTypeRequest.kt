package com.cleevio.cinemax.api.module.screeningtype.controller.dto

import com.cleevio.cinemax.api.module.screeningtype.service.command.CreateOrUpdateScreeningTypeCommand
import java.util.UUID

data class UpdateScreeningTypeRequest(
    val code: String,
    val title: String,
) {

    fun toCommand(screeningTypeId: UUID): CreateOrUpdateScreeningTypeCommand = CreateOrUpdateScreeningTypeCommand(
        id = screeningTypeId,
        code = code,
        title = title
    )
}
