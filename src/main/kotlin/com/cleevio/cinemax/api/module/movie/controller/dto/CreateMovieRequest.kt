package com.cleevio.cinemax.api.module.movie.controller.dto

import com.cleevio.cinemax.api.module.movie.service.command.CreateOrUpdateMovieCommand
import java.time.LocalDate
import java.util.Optional
import java.util.UUID

data class CreateMovieRequest(
    val rawTitle: String,
    val originalTitle: Optional<String>? = null,
    val premiereDate: Optional<LocalDate>? = null,
    val duration: Int,
    val distributorId: UUID,
    val productionId: Optional<UUID>? = null,
    val primaryGenreId: Optional<UUID>? = null,
    val secondaryGenreId: Optional<UUID>? = null,
    val ratingId: Optional<UUID>? = null,
    val technologyId: UUID,
    val languageId: UUID,
    val tmsLanguageId: UUID,
    val jsoIds: Set<UUID>? = null,
) {

    fun toCommand() = CreateOrUpdateMovieCommand(
        rawTitle = rawTitle,
        originalTitle = originalTitle,
        premiereDate = premiereDate,
        duration = duration,
        distributorId = distributorId,
        productionId = productionId,
        primaryGenreId = primaryGenreId,
        secondaryGenreId = secondaryGenreId,
        ratingId = ratingId,
        technologyId = technologyId,
        languageId = languageId,
        tmsLanguageId = tmsLanguageId,
        jsoIds = jsoIds
    )
}
