package com.cleevio.cinemax.api.module.product.controller.dto

import com.cleevio.cinemax.api.common.util.jackson.BigDecimalQuantitySerializer
import com.cleevio.cinemax.api.module.product.constant.ProductType
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import com.fasterxml.jackson.databind.annotation.JsonSerialize
import io.swagger.v3.oas.annotations.media.Schema
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class AdminGetProductResponse(
    val id: UUID,
    val title: String,
    val code: String,
    val active: Boolean,
    val type: ProductType,
    val price: BigDecimal,
    val flagshipPrice: BigDecimal?,
    val priceNoVat: BigDecimal,
    val soldInBuffet: Boolean,
    val soldInCafe: <PERSON>ole<PERSON>,
    val soldInVip: Boolean,
    val order: Int?,
    val tabletOrder: Int?,
    val isPackagingDeposit: Boolean,
    @field:JsonSerialize(using = BigDecimalQuantitySerializer::class)
    val stockQuantity: BigDecimal,
    val stockQuantityThreshold: Int?,
    val discountAmount: BigDecimal?,
    val discountPercentage: Int?,
    val taxRate: Int?,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
    val productCategory: ProductCategoryResponse,
    val imageFile: ImageFileResponse?,
    val productComposition: List<ProductCompositionResponse>,
) {
    @Schema(name = "AdminGetProductCategoryResponse")
    data class ProductCategoryResponse(
        val id: UUID,
        val type: ProductCategoryType,
        val title: String,
    )

    @Schema(name = "AdminGetProductImageFileResponse")
    data class ImageFileResponse(
        val id: UUID,
        val url: String,
    )

    @Schema(name = "AdminGetProductCompositionResponse")
    data class ProductCompositionResponse(
        val productComponent: ProductComponentResponse?,
        val productInProduct: ProductInProductResponse?,
    )

    @Schema(name = "AdminGetProductComponentResponse")
    data class ProductComponentResponse(
        val id: UUID,
        val title: String,
        @field:JsonSerialize(using = BigDecimalQuantitySerializer::class)
        val quantity: BigDecimal,
        val unit: ProductComponentUnit,
    )

    @Schema(name = "AdminGetProductInProductResponse")
    data class ProductInProductResponse(
        val id: UUID,
        val title: String,
        @field:JsonSerialize(using = BigDecimalQuantitySerializer::class)
        val quantity: BigDecimal,
        val unit: ProductComponentUnit,
        val productInProductPrice: BigDecimal?,
        val productInProductFlagshipPrice: BigDecimal?,
    )
}
