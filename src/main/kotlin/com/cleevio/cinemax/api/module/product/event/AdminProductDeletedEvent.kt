package com.cleevio.cinemax.api.module.product.event

import com.cleevio.cinemax.api.common.integration.pubsub.constant.MessageType
import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.util.toJsonString
import com.cleevio.cinemax.api.module.product.entity.Product
import java.util.UUID

data class AdminProductDeletedEvent(
    val code: String,
) {
    fun toMessagePayload() = MessagePayload(
        id = UUID.randomUUID(),
        type = MessageType.PRODUCT_DELETED,
        data = this.toJsonString()
    )
}

fun Product.toMessagingDeleteEvent() = AdminProductDeletedEvent(
    code = this.code
)
