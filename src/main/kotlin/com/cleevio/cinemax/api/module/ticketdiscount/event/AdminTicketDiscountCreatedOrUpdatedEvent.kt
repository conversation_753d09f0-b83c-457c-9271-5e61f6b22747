package com.cleevio.cinemax.api.module.ticketdiscount.event

import com.cleevio.cinemax.api.common.integration.pubsub.constant.MessageType
import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.util.toJsonString
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountType
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.fasterxml.jackson.annotation.JsonInclude
import java.math.BigDecimal
import java.util.UUID

@JsonInclude(JsonInclude.Include.NON_NULL)
data class AdminTicketDiscountCreatedOrUpdatedEvent(
    val code: String,
    val title: String? = null,
    val type: TicketDiscountType,
    val usageType: TicketDiscountUsageType,
    val amount: BigDecimal? = null,
    val percentage: Int? = null,
    val applicableToCount: Int? = null,
    val freeCount: Int? = null,
    val zeroFees: <PERSON><PERSON><PERSON>,
    val voucherOnly: Boolean,
    val active: Boolean,
    val order: Int? = null,
) {
    fun toMessagePayload() = MessagePayload(
        id = UUID.randomUUID(),
        type = MessageType.TICKET_DISCOUNT_CREATED_OR_UPDATED,
        data = this.toJsonString()
    )
}

fun TicketDiscount.toMessagingEvent() = AdminTicketDiscountCreatedOrUpdatedEvent(
    code = this.code,
    title = this.title,
    type = this.type,
    usageType = this.usageType,
    amount = this.amount,
    percentage = this.percentage,
    applicableToCount = this.applicableToCount,
    freeCount = this.freeCount,
    zeroFees = this.zeroFees,
    voucherOnly = this.voucherOnly,
    active = this.active,
    order = this.order
)
