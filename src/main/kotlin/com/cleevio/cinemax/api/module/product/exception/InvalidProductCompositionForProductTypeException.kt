package com.cleevio.cinemax.api.module.product.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
class InvalidProductCompositionForProductTypeException : ApiException(
    Module.PRODUCT,
    ProductErrorType.INVALID_PRODUCT_COMPOSITION_FOR_PRODUCT_TYPE
)
