package com.cleevio.cinemax.api.module.basketitem.util

import com.cleevio.cinemax.api.module.basketitem.constant.BasketItemType
import com.cleevio.cinemax.api.module.basketitem.exception.MultipleProductDiscountsException
import com.cleevio.cinemax.api.module.basketitem.exception.MultipleTicketsForOneSeatException
import com.cleevio.cinemax.api.module.basketitem.exception.ProductBasketItemWithoutProductException
import com.cleevio.cinemax.api.module.basketitem.exception.TicketBasketItemWithoutIsGroupTicketException
import com.cleevio.cinemax.api.module.basketitem.exception.TicketBasketItemWithoutReservationException
import com.cleevio.cinemax.api.module.basketitem.exception.TicketBasketItemWithoutTicketPriceException
import com.cleevio.cinemax.api.module.basketitem.exception.TwoDiscountCardsAppliedOnTicketBasketItemException
import com.cleevio.cinemax.api.module.basketitem.service.command.CreateBasketItemCommand

fun validateTicketBasketItemQuantity(quantity: Int) {
    if (quantity > 1) throw MultipleTicketsForOneSeatException()
}

fun validateProductDiscountBasketItemQuantity(quantity: Int) {
    if (quantity > 1) throw MultipleProductDiscountsException()
}

fun validateCreateBasketItemCommand(command: CreateBasketItemCommand) {
    when (command.type) {
        BasketItemType.TICKET -> {
            if (command.reservationSeatId == null) {
                throw TicketBasketItemWithoutReservationException()
            }
            if (command.priceCategoryItemNumber == null) {
                throw TicketBasketItemWithoutTicketPriceException()
            }
            if (command.isGroupTicket == null) {
                throw TicketBasketItemWithoutIsGroupTicketException()
            }
            if (command.primaryTicketDiscountCardId != null && command.secondaryTicketDiscountCardId != null) {
                throw TwoDiscountCardsAppliedOnTicketBasketItemException()
            }
        }
        BasketItemType.PRODUCT -> {
            if (command.productId == null) {
                throw ProductBasketItemWithoutProductException()
            }
        }
        BasketItemType.PRODUCT_DISCOUNT -> {}
    }
}

fun parseProdmenuQuantity(count: String): Int {
    val intString = count.trim().filter { it.isDigit() }
    if (intString.isEmpty()) {
        return 1
    }
    return intString.toInt()
}
