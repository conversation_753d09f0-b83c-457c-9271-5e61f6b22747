package com.cleevio.cinemax.api.module.table.service

import com.cleevio.cinemax.mssql.rp_bufete.dbo.tables.pojos.Stoly
import org.springframework.stereotype.Repository

@Repository
class TableMssqlFinderService(
    private val tableMssqlFinderRepository: TableMssqlFinderRepository,
) {

    fun findByOriginalId(originalId: Int): Stoly? {
        return tableMssqlFinderRepository.findByOriginalId(originalId)
    }
}
