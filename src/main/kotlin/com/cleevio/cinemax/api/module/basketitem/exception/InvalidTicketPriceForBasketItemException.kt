package com.cleevio.cinemax.api.module.basketitem.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
class InvalidTicketPriceForBasketItemException : ApiException(
    Module.BASKET_ITEM,
    BasketItemErrorType.INVALID_TICKET_PRICE_FOR_BASKET_ITEM
)
