package com.cleevio.cinemax.api.module.outboxevent.service

import com.cleevio.cinemax.api.module.outboxevent.entity.OutboxEvent
import com.cleevio.cinemax.api.module.outboxevent.exception.OutboxEventNotFoundException
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class OutboxEventFinderService(
    private val outboxEventFinderRepository: OutboxEventFinderRepository,
) {

    fun findAll(): List<OutboxEvent> {
        return outboxEventFinderRepository.findAll()
    }

    fun findById(id: UUID): OutboxEvent? {
        return outboxEventFinderRepository.findById(id)
    }

    fun findAllByEntityId(id: UUID): List<OutboxEvent> {
        return outboxEventFinderRepository.findAllByEntityId(id)
    }

    fun getById(id: UUID): OutboxEvent {
        return findById(id) ?: throw OutboxEventNotFoundException()
    }

    fun findAllByEventStateFailedFromTodayOrderByCreatedAt(): List<OutboxEvent> {
        return outboxEventFinderRepository.findAllByEventStateFailedFromTodayOrderByCreatedAt()
    }
}
