package com.cleevio.cinemax.api.module.jso.service

import com.cleevio.cinemax.api.module.jso.entity.Jso
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface JsoRepository : JpaRepository<Jso, UUID> {

    fun findByCode(code: String): Jso?

    fun findAllByIdIn(ids: Set<UUID>): List<Jso>

    fun findAllByCodeIn(codes: Set<String>): List<Jso>
}
