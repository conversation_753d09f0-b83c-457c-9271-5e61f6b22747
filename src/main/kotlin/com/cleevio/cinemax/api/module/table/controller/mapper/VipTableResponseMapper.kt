package com.cleevio.cinemax.api.module.table.controller.mapper

import com.cleevio.cinemax.api.module.table.controller.dto.VipTableResponse
import com.cleevio.cinemax.api.module.table.entity.Table
import org.springframework.stereotype.Component

@Component
class VipTableResponseMapper {

    fun mapList(
        tables: List<Table>,
    ): List<VipTableResponse> {
        return tables.map { mapSingle(it) }
    }

    private fun mapSingle(
        table: Table,
    ): VipTableResponse {
        return VipTableResponse(
            id = table.id,
            title = table.title,
            originalId = table.originalId,
            ipAddress = table.ipAddress
        )
    }
}
