package com.cleevio.cinemax.api.module.basketitem.event.listener

import com.cleevio.cinemax.api.module.basketitem.event.TicketBasketItemDiscountRemovalInitiatedEvent
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemService
import com.cleevio.cinemax.api.module.basketitem.service.command.PatchBasketItemCommand
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
class BasketBasketItemEventListener(
    private val basketItemService: BasketItemService,
) {

    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    fun listenToTicketBasketItemDiscountRemovalInitiatedEvent(event: TicketBasketItemDiscountRemovalInitiatedEvent) {
        basketItemService.patchBasketItem(
            PatchBasketItemCommand(
                basketId = event.basketId,
                basketItemId = event.basketItemId,
                primaryTicketDiscountId = event.primaryTicketDiscountId,
                secondaryTicketDiscountId = event.secondaryTicketDiscountId,
                patchDeletedBasketItems = true
            )
        )
    }
}
