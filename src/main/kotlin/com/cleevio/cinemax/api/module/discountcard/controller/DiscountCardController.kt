package com.cleevio.cinemax.api.module.discountcard.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.IdResult
import com.cleevio.cinemax.api.module.discountcard.controller.dto.DiscountCardResponse
import com.cleevio.cinemax.api.module.discountcard.controller.mapper.DiscountCardResponseMapper
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardService
import com.cleevio.cinemax.api.module.discountcard.service.command.ActivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.DeactivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.ValidateDiscountCardCommand
import io.swagger.v3.oas.annotations.tags.Tag
import org.springframework.http.HttpStatus
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RequestParam
import org.springframework.web.bind.annotation.ResponseStatus
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "POS Discount Cards")
@RestController
@RequestMapping("/pos-app/discount-cards")
class DiscountCardController(
    private val discountCardService: DiscountCardService,
    private val discountCardResponseMapper: DiscountCardResponseMapper,
) {

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/{discountCardCode}/validate", produces = [ApiVersion.VERSION_1_JSON])
    fun validateDiscountCardDeprecated(
        @PathVariable discountCardCode: String,
        @RequestParam basketId: UUID,
        @RequestParam posConfigurationId: UUID,
    ): DiscountCardResponse = discountCardService.validateDiscountCard(
        ValidateDiscountCardCommand(
            discountCardCode = discountCardCode,
            basketId = basketId,
            posConfigurationId = posConfigurationId
        )
    ).let { discountCardResponseMapper.mapSingle(it) }

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/{discountCardCode}/validate", produces = [ApiVersion.VERSION_2_JSON])
    fun validateDiscountCard(
        @PathVariable discountCardCode: String,
        @RequestParam basketId: UUID,
        @RequestParam posConfigurationId: UUID,
    ) = IdResult(
        discountCardService.validateDiscountCard(
            ValidateDiscountCardCommand(
                discountCardCode = discountCardCode,
                basketId = basketId,
                posConfigurationId = posConfigurationId
            )
        ).id
    )

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/{discountCardId}/activate", produces = [ApiVersion.VERSION_1_JSON])
    fun activateDiscountCard(
        @PathVariable discountCardId: UUID,
        @RequestParam(required = false) screeningId: UUID? = null,
        @RequestParam basketId: UUID,
        @RequestParam posConfigurationId: UUID,
    ): DiscountCardResponse {
        discountCardService.activateDiscountCard(
            ActivateDiscountCardCommand(
                discountCardId = discountCardId,
                screeningId = screeningId,
                basketId = basketId,
                posConfigurationId = posConfigurationId
            )
        ).let {
            return discountCardResponseMapper.mapSingle(it)
        }
    }

    @PreAuthorize(Role.CASHIER_AND_MANAGER)
    @PostMapping("/{discountCardId}/deactivate", produces = [ApiVersion.VERSION_1_JSON])
    @ResponseStatus(value = HttpStatus.NO_CONTENT)
    fun deactivateDiscountCard(
        @PathVariable discountCardId: UUID,
        @RequestParam basketId: UUID,
    ) {
        discountCardService.deactivateDiscountCard(
            DeactivateDiscountCardCommand(
                discountCardId = discountCardId,
                basketId = basketId
            )
        )
    }
}
