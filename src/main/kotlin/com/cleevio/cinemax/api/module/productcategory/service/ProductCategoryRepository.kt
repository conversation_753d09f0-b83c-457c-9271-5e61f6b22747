package com.cleevio.cinemax.api.module.productcategory.service

import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ProductCategoryRepository : JpaRepository<ProductCategory, UUID> {

    fun findByIdAndDeletedAtIsNull(id: UUID): ProductCategory?

    fun findAllByIdInAndDeletedAtIsNull(ids: Set<UUID>): List<ProductCategory>

    fun findByCode(originalCode: String): ProductCategory?

    fun findByCodeAndDeletedAtIsNull(originalCode: String): ProductCategory?

    fun existsByCode(code: String): Boolean
}
