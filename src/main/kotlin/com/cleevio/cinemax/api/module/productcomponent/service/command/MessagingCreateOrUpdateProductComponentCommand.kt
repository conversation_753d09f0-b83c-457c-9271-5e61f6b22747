package com.cleevio.cinemax.api.module.productcomponent.service.command

import com.cleevio.cinemax.api.common.validation.ValidTaxRate
import com.cleevio.cinemax.api.module.productcomponent.constant.ProductComponentUnit
import jakarta.validation.constraints.NotBlank
import java.math.BigDecimal
import java.util.UUID

data class MessagingCreateOrUpdateProductComponentCommand(
    val productComponentCategoryId: UUID,

    @field:NotBlank
    val code: String,

    @field:NotBlank
    val title: String,
    val unit: ProductComponentUnit,
    val purchasePrice: BigDecimal,
    val active: <PERSON>olean,

    @field:ValidTaxRate
    val taxRateOverride: Int? = null,
)
