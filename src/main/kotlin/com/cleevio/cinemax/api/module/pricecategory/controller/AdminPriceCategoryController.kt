package com.cleevio.cinemax.api.module.pricecategory.controller

import com.cleevio.cinemax.api.common.constant.ApiVersion
import com.cleevio.cinemax.api.common.constant.Role
import com.cleevio.cinemax.api.common.dto.IdResult
import com.cleevio.cinemax.api.common.dto.SimplePage
import com.cleevio.cinemax.api.common.dto.toSimplePage
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.AdminGetPriceCategoryResponse
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.AdminSearchPriceCategoriesResponse
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.CreatePriceCategoryRequest
import com.cleevio.cinemax.api.module.pricecategory.controller.dto.UpdatePriceCategoryRequest
import com.cleevio.cinemax.api.module.pricecategory.service.AdminGetPriceCategoryQueryService
import com.cleevio.cinemax.api.module.pricecategory.service.AdminSearchPriceCategoriesQueryService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminGetPriceCategoryQuery
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminSearchPriceCategoriesFilter
import com.cleevio.cinemax.api.module.pricecategory.service.query.AdminSearchPriceCategoriesQuery
import com.cleevio.cinemax.psql.tables.PriceCategoryColumnNames
import io.swagger.v3.oas.annotations.tags.Tag
import org.springdoc.core.annotations.ParameterObject
import org.springframework.data.domain.Pageable
import org.springframework.data.web.PageableDefault
import org.springframework.security.access.prepost.PreAuthorize
import org.springframework.web.bind.annotation.GetMapping
import org.springframework.web.bind.annotation.PathVariable
import org.springframework.web.bind.annotation.PostMapping
import org.springframework.web.bind.annotation.PutMapping
import org.springframework.web.bind.annotation.RequestBody
import org.springframework.web.bind.annotation.RequestMapping
import org.springframework.web.bind.annotation.RestController
import java.util.UUID

@Tag(name = "Manager Price Categories")
@RestController
@RequestMapping("/manager-app/price-categories")
class AdminPriceCategoryController(
    private val adminSearchPriceCategoriesQueryService: AdminSearchPriceCategoriesQueryService,
    private val adminGetPriceCategoryQueryService: AdminGetPriceCategoryQueryService,
    private val priceCategoryService: PriceCategoryService,
) {

    @PreAuthorize(Role.MANAGER)
    @PostMapping("/search", produces = [ApiVersion.VERSION_1_JSON])
    fun searchPriceCategories(
        @ParameterObject
        @PageableDefault(sort = [PriceCategoryColumnNames.TITLE])
        pageable: Pageable,
        @RequestBody filter: AdminSearchPriceCategoriesFilter,
    ): SimplePage<AdminSearchPriceCategoriesResponse> {
        return adminSearchPriceCategoriesQueryService(
            AdminSearchPriceCategoriesQuery(
                pageable = pageable,
                filter = filter
            )
        ).toSimplePage()
    }

    @PreAuthorize(Role.MANAGER)
    @PostMapping(produces = [ApiVersion.VERSION_1_JSON])
    fun createPriceCategory(
        @RequestBody request: CreatePriceCategoryRequest,
    ): IdResult = IdResult(priceCategoryService.adminCreateOrUpdatePriceCategory(request.toCommand()))

    @PreAuthorize(Role.MANAGER)
    @PutMapping("/{priceCategoryId}", produces = [ApiVersion.VERSION_1_JSON])
    fun updatePriceCategory(
        @RequestBody request: UpdatePriceCategoryRequest,
        @PathVariable priceCategoryId: UUID,
    ): IdResult = IdResult(priceCategoryService.adminCreateOrUpdatePriceCategory(request.toCommand(priceCategoryId)))

    @PreAuthorize(Role.MANAGER)
    @GetMapping("/{priceCategoryId}", produces = [ApiVersion.VERSION_1_JSON])
    fun getPriceCategory(
        @PathVariable priceCategoryId: UUID,
    ): AdminGetPriceCategoryResponse = adminGetPriceCategoryQueryService(
        AdminGetPriceCategoryQuery(
            priceCategoryId = priceCategoryId
        )
    )
}
