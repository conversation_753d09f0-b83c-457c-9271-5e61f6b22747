package com.cleevio.cinemax.api.module.dailyclosing.service

import com.cleevio.cinemax.api.common.constant.DAILY_CLOSING
import com.cleevio.cinemax.api.common.util.ifTrue
import com.cleevio.cinemax.api.common.util.isNotEqualTo
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.mapNotNullToSet
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basketitem.entity.BasketItem
import com.cleevio.cinemax.api.module.basketitem.service.BasketItemJpaFinderService
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingLockValues.CALCULATE_DAILY_CLOSING
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingLockValues.DEDUCT_DAILY_CLOSING
import com.cleevio.cinemax.api.module.dailyclosing.constant.DailyClosingState
import com.cleevio.cinemax.api.module.dailyclosing.entity.DailyClosing
import com.cleevio.cinemax.api.module.dailyclosing.exception.DailyClosingCashBalanceNotZeroException
import com.cleevio.cinemax.api.module.dailyclosing.exception.DailyClosingWithNoDeductionMovementException
import com.cleevio.cinemax.api.module.dailyclosing.model.DailyClosingCountModel
import com.cleevio.cinemax.api.module.dailyclosingmovement.constant.DailyClosingMovementItemSubtype
import com.cleevio.cinemax.api.module.dailyclosingmovement.entity.DailyClosingMovement
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementJpaFinderService
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.DailyClosingMovementService
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.CreateOrUpdateBaseDailyClosingMovementsCommand
import com.cleevio.cinemax.api.module.dailyclosingmovement.service.command.CreateOrUpdateDeductionDailyClosingMovementCommand
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJpaFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketJpaFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.service.TicketDiscountJpaFinderService
import com.cleevio.library.lockinghandler.service.Lock
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal
import java.time.LocalDateTime
import java.time.Month
import java.util.UUID

@Service
@Validated
class DailyClosingService(
    private val posConfigurationJpaFinderService: PosConfigurationJpaFinderService,
    private val dailyClosingJpaFinderService: DailyClosingJpaFinderService,
    private val basketJpaFinderService: BasketJpaFinderService,
    private val basketItemJpaFinderService: BasketItemJpaFinderService,
    private val dailyClosingMovementService: DailyClosingMovementService,
    private val receiptNumberGenerator: DailyClosingReceiptNumberGenerator,
    private val dailyClosingRepository: DailyClosingRepository,
    private val dailyClosingMovementJpaFinderService: DailyClosingMovementJpaFinderService,
    private val ticketJpaFinderService: TicketJpaFinderService,
    private val ticketDiscountJpaFinderService: TicketDiscountJpaFinderService,
) {
    private val log = logger()

    @Transactional
    @Lock(DAILY_CLOSING, CALCULATE_DAILY_CLOSING)
    fun calculate() {
        val now = LocalDateTime.now()

        val posConfigurationsIds = posConfigurationJpaFinderService.findAll().mapToSet { it.id }
        val existingLatestDailyClosingsMap = dailyClosingJpaFinderService.findLatestDailyClosingForAllPosConfigurationIds(
            posConfigurationIds = posConfigurationsIds
        ).associateBy { it.posConfigurationId }
        val posConfigurationIdToLatestDailyClosingMap = posConfigurationsIds.associateWith {
            existingLatestDailyClosingsMap[it]
        }

        val baskets = basketJpaFinderService.findAllNonDeletedPaidByPosConfigurationIdIn(posConfigurationsIds)
        val posConfigurationIdToBasketsMap = posConfigurationIdToLatestDailyClosingMap.mapValues {
            val timeRange = (it.value?.closedAt ?: it.value?.previousClosedAt ?: START_OF_THE_YEAR)..now
            baskets.filter { basket -> basket.paymentPosConfigurationId == it.key && basket.createdAt in timeRange }
        }.filter {
            // if POS has no paid baskets, skip it from daily closing calculation
            it.value.isNotEmpty()
        }
        val basketIdToBasketItemsMap = basketItemJpaFinderService.findAllNonDeletedByBasketIdIn(
            basketIds = posConfigurationIdToBasketsMap.values.flatten().mapToSet { it.id }
        ).groupBy { it.basketId }

        val posConfigurationIdToBasketItemsMap = posConfigurationIdToBasketsMap.mapValues {
            it.value.flatMap { basket -> basketIdToBasketItemsMap[basket.id].orEmpty() }
        }

        val ticketDiscountIdToFixedPriceAmount = ticketDiscountJpaFinderService.findAllFixedPriceDiscounts().associate {
            it.id to it.amount
        }
        val ticketIdToFixedPriceAmount = ticketJpaFinderService.findAllNonDeletedByIdIn(
            basketIdToBasketItemsMap.values.flatten().mapNotNullToSet { it.ticketId }
        ).filter {
            it.ticketDiscountPrimaryId in ticketDiscountIdToFixedPriceAmount.keys ||
                it.ticketDiscountSecondaryId in ticketDiscountIdToFixedPriceAmount.keys
        }.associate {
            it.id to setOfNotNull(
                ticketDiscountIdToFixedPriceAmount[it.ticketDiscountPrimaryId],
                ticketDiscountIdToFixedPriceAmount[it.ticketDiscountSecondaryId]
            ).firstOrNull()
        }

        posConfigurationIdToLatestDailyClosingMap.forEach { (posConfigurationId, latestClosing) ->
            val basketItems = posConfigurationIdToBasketItemsMap[posConfigurationId] ?: return@forEach
            createOrUpdate(
                existingDailyClosing = latestClosing,
                posConfigurationId = posConfigurationId,
                basketItems = basketItems,
                ticketIdToFixedPriceAmount = ticketIdToFixedPriceAmount,
                now = now
            ).also { dailyClosingId ->
                dailyClosingMovementService.createOrUpdateBaseMovements(
                    CreateOrUpdateBaseDailyClosingMovementsCommand(
                        dailyClosingId = dailyClosingId,
                        basketItemIds = posConfigurationIdToBasketItemsMap[posConfigurationId].orEmpty()
                            .mapToSet { item -> item.id }
                    )
                )
            }
        }
    }

    @Transactional
    @Lock(DAILY_CLOSING, DEDUCT_DAILY_CLOSING)
    fun deduct() {
        val dailyClosings = dailyClosingJpaFinderService.findAllOpenDailyClosingsOfPhysicalPOSs().ifEmpty {
            return
        }

        createDeductionMovementForEmptyDailyClosings(dailyClosings)

        dailyClosingMovementJpaFinderService.findAllNonDeletedCashMovementsByDailyClosingIds(
            dailyClosings.mapToSet { it.id }
        ).let {
            validateDeductionMovementExistsForEachDailyClosing(it)
            validateTotalCashBalanceEqualsZero(it)
        }

        val now = LocalDateTime.now()
        dailyClosings.forEach {
            dailyClosingRepository.save(
                it.apply {
                    state = DailyClosingState.CLOSED
                    closedAt = now
                }
            )
        }
    }

    @Transactional
    @Lock(DAILY_CLOSING, DEDUCT_DAILY_CLOSING)
    fun deductOnlinePosConfiguration() {
        val onlineDailyClosing = dailyClosingJpaFinderService.findAllOpenDailyClosingsOfOnlinePOSs().firstOrNull() ?: run {
            log.error("Online POS configuration not defined. Can't deduct its daily closing.")
            return
        }

        dailyClosingMovementService.createOrUpdateDeductionMovement(
            command = CreateOrUpdateDeductionDailyClosingMovementCommand(onlineDailyClosing.id)
        )

        dailyClosingRepository.save(
            onlineDailyClosing.apply {
                state = DailyClosingState.CLOSED
                closedAt = LocalDateTime.now()
            }
        )
    }

    private fun createDeductionMovementForEmptyDailyClosings(dailyClosings: List<DailyClosing>) {
        dailyClosingMovementJpaFinderService.findAllDailyClosingIdsWithEmptyCashMovements(
            dailyClosings.mapToSet { it.id }
        ).forEach {
            dailyClosingMovementService.createOrUpdateDeductionMovement(
                command = CreateOrUpdateDeductionDailyClosingMovementCommand(it)
            )
        }
    }

    private fun createOrUpdate(
        posConfigurationId: UUID,
        basketItems: List<BasketItem>,
        ticketIdToFixedPriceAmount: Map<UUID, BigDecimal?>,
        existingDailyClosing: DailyClosing?,
        now: LocalDateTime,
    ): UUID {
        val countModel = DailyClosingCountModel(
            basketItems = basketItems,
            ticketIdToFixedPriceAmount = ticketIdToFixedPriceAmount
        )

        val dailyClosing = if (existingDailyClosing == null || existingDailyClosing.state == DailyClosingState.CLOSED) {
            mapToNewDailyClosing(
                posConfigurationId = posConfigurationId,
                receiptNumber = receiptNumberGenerator.generateDailyClosingReceiptNumber(now),
                countModel = countModel,
                previousClosedAt = existingDailyClosing?.closedAt
            )
        } else {
            mapToExistingDailyClosing(
                existingDailyClosing = existingDailyClosing,
                countModel = countModel
            )
        }

        return dailyClosingRepository.save(dailyClosing).id
    }

    private fun mapToNewDailyClosing(
        posConfigurationId: UUID,
        receiptNumber: String,
        countModel: DailyClosingCountModel,
        previousClosedAt: LocalDateTime?,
    ) = DailyClosing(
        posConfigurationId = posConfigurationId,
        receiptNumber = receiptNumber,
        ticketsCount = countModel.ticketsCount,
        cancelledTicketsCount = countModel.cancelledTicketsCount,
        fixedPriceTicketsCount = countModel.fixedPriceTicketsCount,
        fixedPriceTicketsAmount = countModel.fixedPriceTicketsAmount,
        productsCount = countModel.productsCount,
        cancelledProductsCount = countModel.cancelledProductsCount,
        previousClosedAt = previousClosedAt
    )

    private fun mapToExistingDailyClosing(
        existingDailyClosing: DailyClosing,
        countModel: DailyClosingCountModel,
    ) = existingDailyClosing.apply {
        ticketsCount = countModel.ticketsCount
        cancelledTicketsCount = countModel.cancelledTicketsCount
        fixedPriceTicketsCount = countModel.fixedPriceTicketsCount
        fixedPriceTicketsAmount = countModel.fixedPriceTicketsAmount
        productsCount = countModel.productsCount
        cancelledProductsCount = countModel.cancelledProductsCount
        previousClosedAt = existingDailyClosing.previousClosedAt
    }

    private fun validateDeductionMovementExistsForEachDailyClosing(cashMovements: List<DailyClosingMovement>) {
        cashMovements
            .groupBy { it.dailyClosingId }
            .mapValues { it.value.map { movement -> movement.itemSubtype } }
            .any { it.value.isNotEmpty() && DailyClosingMovementItemSubtype.DEDUCTION !in it.value }
            .ifTrue {
                throw DailyClosingWithNoDeductionMovementException()
            }
    }

    private fun validateTotalCashBalanceEqualsZero(cashMovements: List<DailyClosingMovement>) =
        cashMovements
            .sumOf { it.getAdjustedAmount() }
            .let {
                if (it isNotEqualTo 0.toBigDecimal()) throw DailyClosingCashBalanceNotZeroException()
            }
}

private val START_OF_THE_YEAR = LocalDateTime.of(LocalDateTime.now().year, Month.JANUARY, 1, 0, 0)
