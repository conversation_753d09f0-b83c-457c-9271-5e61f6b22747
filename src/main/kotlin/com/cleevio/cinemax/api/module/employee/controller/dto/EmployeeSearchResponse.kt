package com.cleevio.cinemax.api.module.employee.controller.dto

import com.cleevio.cinemax.api.module.employee.constant.EmployeeRole
import java.time.LocalDateTime
import java.util.UUID

data class EmployeeSearchResponse(
    val id: UUID,
    val username: String,
    val fullName: String? = null,
    val posName: String? = null,
    val role: EmployeeRole,
    val lastLoginAt: LocalDateTime? = null,
    val accessibleAt: LocalDateTime? = null,
    val createdAt: LocalDateTime,
    val updatedAt: LocalDateTime,
)
