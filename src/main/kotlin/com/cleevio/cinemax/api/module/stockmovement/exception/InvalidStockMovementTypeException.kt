package com.cleevio.cinemax.api.module.stockmovement.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.BAD_REQUEST)
class InvalidStockMovementTypeException : ApiException(
    Module.STOCK_MOVEMENT,
    StockMovementErrorType.INVALID_STOCK_MOVEMENT_TYPE
)
