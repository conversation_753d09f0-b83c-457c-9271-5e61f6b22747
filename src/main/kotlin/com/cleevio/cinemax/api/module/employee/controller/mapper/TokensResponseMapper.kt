package com.cleevio.cinemax.api.module.employee.controller.mapper

import com.cleevio.cinemax.api.module.employee.controller.dto.TokensResponse
import com.cleevio.cinemax.api.module.employee.service.model.TokensModel
import org.springframework.stereotype.Component

@Component
class TokensResponseMapper {

    fun mapSingle(model: TokensModel) = TokensResponse(
        accessToken = model.accessToken,
        accessExpiresIn = model.accessExpiresIn,
        refreshToken = model.refreshToken,
        refreshExpiresIn = model.refreshExpiresIn
    )
}
