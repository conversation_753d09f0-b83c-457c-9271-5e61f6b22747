package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.api.module.reservation.constant.RESERVED_STATES
import com.cleevio.cinemax.api.module.reservation.entity.Reservation
import com.cleevio.cinemax.api.module.reservation.exception.ReservationNotFoundException
import org.springframework.data.repository.findByIdOrNull
import org.springframework.stereotype.Service
import java.util.UUID

@Service
class ReservationJpaFinderService(
    private val reservationRepository: ReservationRepository,
) {

    fun findNonDeletedById(id: UUID): Reservation? = reservationRepository.findByIdAndDeletedAtIsNull(id)

    fun getNonDeletedById(id: UUID): Reservation = findNonDeletedById(id)
        ?: throw ReservationNotFoundException()

    fun getById(id: UUID): Reservation = reservationRepository.findByIdOrNull(id)
        ?: throw ReservationNotFoundException()

    fun findLatestByOriginalId(originalId: Int): Reservation? =
        reservationRepository.findFirstByOriginalIdOrderByUpdatedAtDesc(originalId)

    fun findAllExistingByScreeningIdAndSeatIds(screeningId: UUID, seatIds: Set<UUID>): List<Reservation> =
        reservationRepository.findAllByScreeningIdAndSeatIdInAndStateIsInAndDeletedAtIsNull(
            screeningId = screeningId,
            seatIds = seatIds,
            reservationStates = RESERVED_STATES
        )

    fun findAllNonDeletedByGroupReservationId(groupReservationId: UUID): List<Reservation> =
        reservationRepository.findAllByGroupReservationIdAndDeletedAtIsNull(groupReservationId)

    fun findAllNonDeletedByIdIn(reservationIds: Set<UUID>): List<Reservation> =
        reservationRepository.findAllByIdInAndDeletedAtIsNull(reservationIds)
}
