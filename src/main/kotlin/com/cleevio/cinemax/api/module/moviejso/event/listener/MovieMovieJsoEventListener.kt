package com.cleevio.cinemax.api.module.moviejso.event.listener

import com.cleevio.cinemax.api.module.movie.event.MovieWithJsosCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.moviejso.service.MovieJsoService
import com.cleevio.cinemax.api.module.moviejso.service.command.DeleteAndCreateMovieJsosCommand
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.movie.moviejso.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class MovieMovieJsoEventListener(
    private val movieJsoService: MovieJsoService,
) {
    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToMovieWithJsosCreatedOrUpdatedEvent(event: MovieWithJsosCreatedOrUpdatedEvent) {
        movieJsoService.deleteAndCreateMovieJsos(
            DeleteAndCreateMovieJsosCommand(
                movieId = event.movieId,
                jsoIds = event.jsoIds
            )
        )
    }
}
