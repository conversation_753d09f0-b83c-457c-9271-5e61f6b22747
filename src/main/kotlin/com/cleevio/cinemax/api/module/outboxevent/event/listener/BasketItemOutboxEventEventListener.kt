package com.cleevio.cinemax.api.module.outboxevent.event.listener

import com.cleevio.cinemax.api.module.basketitem.event.TableBasketItemDeletedEvent
import com.cleevio.cinemax.api.module.basketitem.event.TableBasketItemUpdatedEvent
import com.cleevio.cinemax.api.module.outboxevent.constant.OutboxEventType
import com.cleevio.cinemax.api.module.outboxevent.service.OutboxEventService
import com.cleevio.cinemax.api.module.outboxevent.service.command.CreateOutboxEventCommand
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.scheduling.annotation.Async
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "features.outbox-event.basket-item.listener.enabled",
    havingValue = "true",
    matchIfMissing = true
)
class BasketItemOutboxEventEventListener(
    private val outboxEventService: OutboxEventService,
) {

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToTableBasketItemDeletedEvent(event: TableBasketItemDeletedEvent) {
        outboxEventService.createOutboxEvent(
            CreateOutboxEventCommand(
                entityId = event.basketItemId,
                type = OutboxEventType.TABLE_BASKET_ITEM_DELETED
            )
        )
    }

    @Async
    @TransactionalEventListener(phase = TransactionPhase.AFTER_COMMIT)
    fun listenToTableBasketItemUpdatedEvent(event: TableBasketItemUpdatedEvent) {
        outboxEventService.createOutboxEvent(
            CreateOutboxEventCommand(
                entityId = event.basketItemId,
                type = OutboxEventType.TABLE_BASKET_ITEM_UPDATED
            )
        )
    }
}
