package com.cleevio.cinemax.api.module.product.service.query

import com.cleevio.cinemax.api.common.service.FileStorageService
import com.cleevio.cinemax.api.common.util.buildUnaccentLikeIgnoreCaseCondition
import com.cleevio.cinemax.api.common.util.inOrNullIfNullOrEmpty
import com.cleevio.cinemax.api.common.util.paginationAndSorting
import com.cleevio.cinemax.api.module.product.controller.dto.AdminSearchProductsResponse
import com.cleevio.cinemax.api.module.screening.service.PRODUCT_COMPONENT_STOCK_QUANTITY
import com.cleevio.cinemax.api.module.screening.service.PRODUCT_IN_PRODUCT_STOCK_QUANTITY
import com.cleevio.cinemax.psql.Tables.PRODUCT
import com.cleevio.cinemax.psql.Tables.PRODUCT_COMPOSITION
import jakarta.validation.Valid
import org.jooq.DSLContext
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.exists
import org.jooq.impl.DSL.min
import org.jooq.impl.DSL.notExists
import org.jooq.impl.DSL.select
import org.jooq.impl.DSL.selectOne
import org.jooq.impl.DSL.`when`
import org.springframework.data.domain.Page
import org.springframework.data.domain.PageImpl
import org.springframework.stereotype.Service
import org.springframework.validation.annotation.Validated
import java.math.BigDecimal

@Service
@Validated
class AdminSearchProductsQueryService(
    private val psqlDslContext: DSLContext,
    private val fileStorageService: FileStorageService,
) {

    operator fun invoke(
        @Valid query: AdminSearchProductsQuery,
    ): Page<AdminSearchProductsResponse> {
        val conditions = listOfNotNull(
            buildUnaccentLikeIgnoreCaseCondition(query.filter.title, PRODUCT.TITLE),
            buildUnaccentLikeIgnoreCaseCondition(query.filter.code, PRODUCT.CODE),
            query.filter.types?.let { PRODUCT.TYPE.`in`(it) },
            PRODUCT.productCategory().TYPE.inOrNullIfNullOrEmpty(query.filter.categoryTypes),
            PRODUCT.TYPE.inOrNullIfNullOrEmpty(query.filter.types),
            query.filter.active?.let { PRODUCT.ACTIVE.eq(it) },
            query.filter.isProductComponent?.let {
                val isProductComponentCondition = selectOne()
                    .from(PRODUCT_COMPOSITION)
                    .where(PRODUCT_COMPOSITION.PRODUCT_ID.eq(PRODUCT.ID)).having(count().eq(1))
                if (it) exists(isProductComponentCondition) else notExists(isProductComponentCondition)
            },
            query.filter.isPackagingDeposit?.let {
                if (it) PRODUCT.IS_PACKAGING_DEPOSIT.isTrue else PRODUCT.IS_PACKAGING_DEPOSIT.isFalse
            },
            PRODUCT.productCategory().ID.inOrNullIfNullOrEmpty(query.filter.categoryIds),
            PRODUCT.DELETED_AT.isNull,
            query.filter.soldInBuffet?.let {
                if (it) PRODUCT.SOLD_IN_BUFFET.isTrue else PRODUCT.SOLD_IN_BUFFET.isFalse
            },
            query.filter.soldInCafe?.let {
                if (it) PRODUCT.SOLD_IN_CAFE.isTrue else PRODUCT.SOLD_IN_CAFE.isFalse
            },
            query.filter.soldInVip?.let {
                if (it) PRODUCT.SOLD_IN_VIP.isTrue else PRODUCT.SOLD_IN_VIP.isFalse
            }
        )

        val count = psqlDslContext.fetchCount(PRODUCT, conditions)

        val stockQuantity = select(
            min(
                `when`(
                    PRODUCT_COMPOSITION.PRODUCT_COMPONENT_ID.isNotNull,
                    PRODUCT_COMPONENT_STOCK_QUANTITY
                ).`when`(
                    PRODUCT_COMPOSITION.PRODUCT_IN_PRODUCT_ID.isNotNull,
                    PRODUCT_IN_PRODUCT_STOCK_QUANTITY
                ).otherwise(BigDecimal.ZERO)
            )
        ).from(PRODUCT_COMPOSITION)
            .leftJoin(PRODUCT_COMPOSITION.productComponent())
            .where(PRODUCT_COMPOSITION.PRODUCT_ID.eq(PRODUCT.ID))
            .asField<BigDecimal>(PRODUCT_STOCK_QUANTITY_ALIAS)

        val isProductComponent = select(count().eq(1))
            .from(PRODUCT_COMPOSITION)
            .where(PRODUCT_COMPOSITION.PRODUCT_ID.eq(PRODUCT.ID))
            .asField<Boolean>(IS_PRODUCT_COMPONENT_ALIAS)

        return psqlDslContext
            .select(
                PRODUCT.ID,
                PRODUCT.CODE,
                PRODUCT.TITLE,
                PRODUCT.TYPE,
                PRODUCT.ORDER,
                PRODUCT.PRICE,
                PRODUCT.FLAGSHIP_PRICE,
                PRODUCT.ACTIVE,
                PRODUCT.DISCOUNT_AMOUNT,
                PRODUCT.DISCOUNT_PERCENTAGE,
                PRODUCT.CREATED_AT,
                PRODUCT.UPDATED_AT,
                PRODUCT.productCategory().ID,
                PRODUCT.productCategory().TYPE,
                PRODUCT.productCategory().TITLE,
                PRODUCT.file().ID,
                PRODUCT.file().EXTENSION,
                PRODUCT.file().TYPE,
                stockQuantity,
                isProductComponent
            )
            .from(PRODUCT)
            .where(conditions)
            .paginationAndSorting(query.pageable, PRODUCT, PRODUCT.productCategory())
            .fetch()
            .map {
                AdminSearchProductsResponse(
                    id = it[PRODUCT.ID],
                    code = it[PRODUCT.CODE],
                    title = it[PRODUCT.TITLE],
                    type = it[PRODUCT.TYPE],
                    order = it[PRODUCT.ORDER],
                    price = it[PRODUCT.PRICE],
                    flagshipPrice = it[PRODUCT.FLAGSHIP_PRICE],
                    active = it[PRODUCT.ACTIVE],
                    discountAmount = it[PRODUCT.DISCOUNT_AMOUNT],
                    discountPercentage = it[PRODUCT.DISCOUNT_PERCENTAGE],
                    createdAt = it[PRODUCT.CREATED_AT],
                    updatedAt = it[PRODUCT.UPDATED_AT],
                    productCategory = AdminSearchProductsResponse.ProductCategoryResponse(
                        id = it[PRODUCT.productCategory().ID],
                        type = it[PRODUCT.productCategory().TYPE],
                        title = it[PRODUCT.productCategory().TITLE]
                    ),
                    imageFile = it[PRODUCT.file().ID]?.let { fileId ->
                        AdminSearchProductsResponse.ImageFileResponse(
                            id = fileId,
                            url = fileStorageService.getFileUrl(
                                fileId = fileId,
                                fileExtension = it[PRODUCT.file().EXTENSION],
                                fileType = it[PRODUCT.file().TYPE]
                            )
                        )
                    },
                    stockQuantity = it[stockQuantity] ?: 0.toBigDecimal(),
                    isProductComponent = it[isProductComponent]
                )
            }.let {
                PageImpl(it, query.pageable, count.toLong())
            }
    }
}

const val IS_PRODUCT_COMPONENT_ALIAS = "is_product_component"
const val PRODUCT_STOCK_QUANTITY_ALIAS = "product_stock_quantity"
