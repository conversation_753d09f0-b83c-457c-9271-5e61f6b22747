package com.cleevio.cinemax.api.module.screening.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.DeploymentType
import com.cleevio.cinemax.api.common.constant.SCREENING
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.common.util.setNullIfOnlyZeroes
import com.cleevio.cinemax.api.common.util.setNullIfZero
import com.cleevio.cinemax.api.common.util.trimIfNotBlank
import com.cleevio.cinemax.api.module.auditorium.service.AuditoriumJooqFinderService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutJpaFinderService
import com.cleevio.cinemax.api.module.language.service.LanguageJpaFinderService
import com.cleevio.cinemax.api.module.movie.service.MovieJooqFinderService
import com.cleevio.cinemax.api.module.movie.service.MovieService
import com.cleevio.cinemax.api.module.movie.service.command.UpdateMovieLanguageAndTechnologyCommand
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryJooqFinderService
import com.cleevio.cinemax.api.module.pricecategory.service.PriceCategoryService
import com.cleevio.cinemax.api.module.pricecategory.service.command.AdminCreateOrUpdatePriceCategoryCommand
import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import com.cleevio.cinemax.api.module.pricecategoryitem.service.command.AdminCreateOrUpdatePriceCategoryItemCommand
import com.cleevio.cinemax.api.module.screening.constant.ScreeningLockValues.SYNCHRONIZE_FROM_MSSQL
import com.cleevio.cinemax.api.module.screening.constant.ScreeningState
import com.cleevio.cinemax.api.module.screening.exception.ScreeningNotFoundException
import com.cleevio.cinemax.api.module.screening.service.command.CreateOrUpdateScreeningCommand
import com.cleevio.cinemax.api.module.screeningfee.service.ScreeningFeeService
import com.cleevio.cinemax.api.module.screeningfee.service.command.CreateOrUpdateScreeningFeeCommand
import com.cleevio.cinemax.api.module.screeningtype.service.ScreeningTypeJpaFinderService
import com.cleevio.cinemax.api.module.screeningtypes.service.ScreeningTypesService
import com.cleevio.cinemax.api.module.screeningtypes.service.command.DeleteAndCreateScreeningTypesCommand
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.api.module.technology.service.TechnologyJpaFinderService
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.RPROG
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rprog
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

@Service
class ScreeningMssqlSynchronizationService(
    private val screeningService: ScreeningService,
    private val screeningJooqFinderService: ScreeningJooqFinderService,
    private val screeningFeeService: ScreeningFeeService,
    private val screeningMssqlFinderRepository: ScreeningMssqlFinderRepository,
    private val auditoriumJooqFinderService: AuditoriumJooqFinderService,
    private val auditoriumLayoutJpaFinderService: AuditoriumLayoutJpaFinderService,
    private val movieJooqFinderService: MovieJooqFinderService,
    private val priceCategoryJooqFinderService: PriceCategoryJooqFinderService,
    private val priceCategoryService: PriceCategoryService,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    private val screeningTypesService: ScreeningTypesService,
    private val screeningTypeJpaFinderService: ScreeningTypeJpaFinderService,
    private val languageJpaFinderService: LanguageJpaFinderService,
    private val technologyJpaFinderService: TechnologyJpaFinderService,
    private val movieService: MovieService,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Rprog>(
    mssqlEntityName = RPROG.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.SCREENING
) {
    private val log = logger()

    @TryLock(SCREENING, SYNCHRONIZE_FROM_MSSQL)
    fun synchronizeAll() = synchronizeAll {
        when (cinemaxConfigProperties.deploymentType) {
            DeploymentType.HEADQUARTERS -> {
                val persistedDate = synchronizationFromMssqlFinderService.findByType(
                    type = SynchronizationFromMssqlType.SCREENING
                )?.toLocalDate()

                screeningMssqlFinderRepository.findAllByDateGt(
                    date = if (persistedDate != null && persistedDate.atStartOfDay().isBefore(HQ_SYNCS_DATE_LIMIT)) {
                        HQ_SYNCS_DATE_LIMIT.toLocalDate()
                    } else {
                        persistedDate
                    }
                )
            }

            DeploymentType.BRANCH -> {
                screeningMssqlFinderRepository.findAllByUpdatedAtGt(
                    synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.SCREENING)
                )
            }
        }
    }

    @TryLock(SCREENING, SYNCHRONIZE_FROM_MSSQL)
    fun synchronizeAllUpdatedSinceLastWeek() = synchronizeAllWithoutTimestampWrite {
        screeningMssqlFinderRepository.findAllByUpdatedAtGt(
            LocalDateTime.now().minusWeeks(1)
        )
    }

    override fun validateAndPersist(mssqlEntity: Rprog) {
        val auditorium = auditoriumJooqFinderService.findByOriginalCode(mssqlEntity.csalu.toInt()) ?: run {
            log.warn(
                "Auditorium with originalCode=${mssqlEntity.csalu} not found. " +
                    "Skipping sync of Screening originalId=${mssqlEntity.rprogid}."
            )
            return
        }

        val movie = movieJooqFinderService.findNonDeletedByOriginalId(mssqlEntity.rfilmid) ?: run {
            log.warn(
                "Movie with originalId=${mssqlEntity.rfilmid} not found. " +
                    "Skipping sync of Screening originalId=${mssqlEntity.rprogid}."
            )
            return
        }

        val (priceCategory, auditoriumLayout) = when (cinemaxConfigProperties.deploymentType) {
            DeploymentType.BRANCH -> {
                val priceCategory = priceCategoryJooqFinderService.findByOriginalId(mssqlEntity.rvstid) ?: run {
                    log.warn(
                        "Price category with originalId=${mssqlEntity.rvstid} not found. " +
                            "Skipping sync of Screening originalId=${mssqlEntity.rprogid}."
                    )
                    return
                }
                val auditoriumLayout = auditoriumLayoutJpaFinderService.findNonDeletedByOriginalId(mssqlEntity.rupravaid) ?: run {
                    log.warn(
                        "Auditorium layout with originalId=${mssqlEntity.rupravaid} not found. " +
                            "Skipping sync of Screening originalId=${mssqlEntity.rprogid}."
                    )
                    return
                }
                Pair(priceCategory, auditoriumLayout)
            }
            DeploymentType.HEADQUARTERS -> {
                if (priceCategoryJooqFinderService.findAll().isEmpty()) {
                    priceCategoryService.adminCreateOrUpdatePriceCategory(
                        AdminCreateOrUpdatePriceCategoryCommand(
                            title = "Cleevio category",
                            active = true,
                            items = listOf(
                                AdminCreateOrUpdatePriceCategoryItemCommand(
                                    number = PriceCategoryItemNumber.PRICE_1,
                                    title = "Price 1",
                                    price = 8.toBigDecimal(),
                                    discounted = false
                                )
                            )
                        )
                    )
                }

                val priceCategory = priceCategoryJooqFinderService.findAll().first()
                val auditoriumLayout = auditoriumLayoutJpaFinderService.getDefaultByAuditoriumId(auditorium.id)
                Pair(priceCategory, auditoriumLayout)
            }
        }

        screeningService.syncCreateOrUpdateScreening(
            mapToCreateOrUpdateScreeningCommand(
                mssqlScreening = mssqlEntity,
                auditoriumId = auditorium.id,
                auditoriumLayoutId = auditoriumLayout.id,
                movieId = movie.id,
                priceCategoryId = priceCategory.id
            )
        )

        val screening = screeningJooqFinderService.findByOriginalId(
            originalId = mssqlEntity.rprogid
        ) ?: throw ScreeningNotFoundException()

        screeningFeeService.createOrUpdateScreeningFee(
            mapToCreateOrUpdateScreeningFeeCommand(
                mssqlScreening = mssqlEntity,
                screeningId = screening.id
            )
        )

        mapToDeleteAndCreateScreeningTypesCommand(
            screeningId = screening.id,
            mssqlEntity = mssqlEntity
        )?.let {
            screeningTypesService.deleteAndCreateScreeningTypes(it)
        }

        movieService.updateMovieLanguageAndTechnology(
            UpdateMovieLanguageAndTechnologyCommand(
                movieId = movie.id,
                languageId = mssqlEntity.jazyk.trimIfNotBlank()?.let { languageJpaFinderService.findByCode(it)?.id },
                technologyId = mssqlEntity.cfotrm.trimIfNotBlank()?.let { technologyJpaFinderService.findByCode(it)?.id }
            )
        )
    }

    private fun mapToDeleteAndCreateScreeningTypesCommand(
        screeningId: UUID,
        mssqlEntity: Rprog,
    ): DeleteAndCreateScreeningTypesCommand? {
        val screeningTypeCodes = setOf(mssqlEntity.cistyp, mssqlEntity.cistyp2, mssqlEntity.cistyp3, mssqlEntity.cistyp4)
            .mapNotNull { it.trimIfNotBlank() }
            .mapNotNull { it.setNullIfOnlyZeroes() }
            .ifEmpty { return null }
            .toSet()

        return screeningTypeJpaFinderService.findAllByCodeIn(screeningTypeCodes)
            .map { it.id }
            .takeUnless { it.isEmpty() }
            ?.let {
                DeleteAndCreateScreeningTypesCommand(
                    screeningId = screeningId,
                    screeningTypeIds = it.toSet()
                )
            }
    }

    private fun mapToCreateOrUpdateScreeningCommand(
        mssqlScreening: Rprog,
        auditoriumId: UUID,
        movieId: UUID,
        priceCategoryId: UUID,
        auditoriumLayoutId: UUID,
    ) = CreateOrUpdateScreeningCommand(
        originalId = mssqlScreening.rprogid,
        auditoriumId = auditoriumId,
        auditoriumLayoutId = auditoriumLayoutId,
        movieId = movieId,
        priceCategoryId = priceCategoryId,
        date = mssqlScreening.porad.toLocalDate(),
        time = mssqlScreening.porad.toLocalTime(),
        saleTimeLimit = mssqlScreening.casprod.toInt().setNullIfZero() ?: DEFAULT_SALE_TIME_LIMIT,
        stopped = mssqlScreening.stop,
        cancelled = mssqlScreening.odpadlo,
        proCommission = mssqlScreening.osa.toInt(),
        filmFondCommission = mssqlScreening.fk.toInt(),
        distributorCommission = mssqlScreening.pujcovne.toInt(),
        publishOnline = mssqlScreening.naweb,
        state = ScreeningState.PUBLISHED,
        // Surcharges and fees are non-nullable on entity -> default values are set here for convenience to enable non-nullability
        // in command as well. In ScreeningService#syncCreateOrUpdate, these attrs are not processed.
        surchargeVip = BigDecimal.ZERO,
        surchargePremium = BigDecimal.ZERO,
        surchargeImax = BigDecimal.ZERO,
        surchargeUltraX = BigDecimal.ZERO,
        serviceFeeVip = BigDecimal.ZERO,
        serviceFeePremium = BigDecimal.ZERO,
        serviceFeeImax = BigDecimal.ZERO,
        serviceFeeUltraX = BigDecimal.ZERO,
        surchargeDBox = BigDecimal.ZERO,
        serviceFeeGeneral = BigDecimal.ZERO
    )

    private fun mapToCreateOrUpdateScreeningFeeCommand(mssqlScreening: Rprog, screeningId: UUID) =
        CreateOrUpdateScreeningFeeCommand(
            originalScreeningId = mssqlScreening.rprogid,
            screeningId = screeningId,
            surchargeVip = mssqlScreening.priplatekvip ?: 0.toBigDecimal(),
            surchargePremium = mssqlScreening.priplatekpremium ?: 0.toBigDecimal(),
            surchargeImax = mssqlScreening.priplatekimax ?: 0.toBigDecimal(),
            surchargeUltraX = mssqlScreening.priplatekultrax ?: 0.toBigDecimal(),
            serviceFeeVip = mssqlScreening.sluzbyvip ?: 0.toBigDecimal(),
            serviceFeePremium = mssqlScreening.sluzbypremium ?: 0.toBigDecimal(),
            serviceFeeImax = mssqlScreening.sluzbyimax ?: 0.toBigDecimal(),
            serviceFeeUltraX = mssqlScreening.sluzbyultrax ?: 0.toBigDecimal(),
            surchargeDBox = mssqlScreening.sluzbydbox ?: 0.toBigDecimal(),
            serviceFeeGeneral = mssqlScreening.priplsluz ?: 0.toBigDecimal()
        )
}

private const val DEFAULT_SALE_TIME_LIMIT = 30
private val HQ_SYNCS_DATE_LIMIT = LocalDateTime.of(2024, 1, 1, 0, 0)
