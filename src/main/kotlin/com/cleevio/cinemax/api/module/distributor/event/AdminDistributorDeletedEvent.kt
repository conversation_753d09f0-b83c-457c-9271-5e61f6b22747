package com.cleevio.cinemax.api.module.distributor.event

import com.cleevio.cinemax.api.common.integration.pubsub.constant.MessageType
import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.util.toJsonString
import com.cleevio.cinemax.api.module.distributor.entity.Distributor
import java.util.UUID

data class AdminDistributorDeletedEvent(
    val code: String,
) {
    fun toMessagePayload() = MessagePayload(
        id = UUID.randomUUID(),
        type = MessageType.DISTRIBUTOR_DELETED,
        data = this.toJsonString()
    )
}

fun Distributor.toMessagingDeleteEvent() = AdminDistributorDeletedEvent(
    code = this.code
)
