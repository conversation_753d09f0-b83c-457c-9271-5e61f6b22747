package com.cleevio.cinemax.api.module.productcategory.controller.mapper

import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.common.util.toIntDown
import com.cleevio.cinemax.api.module.posconfiguration.constant.ProductMode
import com.cleevio.cinemax.api.module.product.controller.dto.ProductSearchResponse
import com.cleevio.cinemax.api.module.product.entity.Product
import com.cleevio.cinemax.api.module.product.service.ProductJooqFinderService
import com.cleevio.cinemax.api.module.productcategory.constant.ProductCategoryType
import com.cleevio.cinemax.api.module.productcategory.controller.dto.ProductCategorySearchResponse
import com.cleevio.cinemax.api.module.productcategory.entity.ProductCategory
import org.springframework.stereotype.Component
import java.math.BigDecimal
import java.util.UUID

@Component
class ProductCategoryResponseMapper(
    private val productJooqFinderService: ProductJooqFinderService,
) {

    fun mapList(
        productCategories: List<ProductCategory>,
        productModes: List<ProductMode>? = null,
    ): List<ProductCategorySearchResponse> {
        val categoryIds = productCategories.mapToSet { it.id }
        val products = productJooqFinderService.findNonDeletedAndActiveByCategoryIdInAndProductModeIn(categoryIds, productModes)

        val categoryIdToProducts = products
            .sortedWith(compareBy<Product, Int?>(nullsFirst()) { it.order }.thenBy { it.title.lowercase() })
            .groupBy { it.productCategoryId }
        val productIds = categoryIdToProducts.values.flatten().mapToSet { it.id }
        val productIdToStockQuantity = productJooqFinderService.findAllNonDeletedActiveProductStockQuantitiesByIdIn(productIds)

        return productCategories
            .filter { categoryIdToProducts.keys.contains(it.id) }
            .filter { it.type == ProductCategoryType.PRODUCT }
            .map {
                mapSingle(
                    productCategory = it,
                    products = categoryIdToProducts[it.id]!!,
                    productIdToStockQuantity = productIdToStockQuantity
                )
            }
    }

    private fun mapSingle(
        productCategory: ProductCategory,
        products: List<Product>,
        productIdToStockQuantity: Map<UUID, BigDecimal>,
    ): ProductCategorySearchResponse {
        return ProductCategorySearchResponse(
            id = productCategory.id,
            title = productCategory.title,
            type = productCategory.type,
            order = productCategory.order,
            hexColorCode = productCategory.hexColorCode,
            products = products.map {
                mapToProductSearchResponse(
                    product = it,
                    stockQuantity = productIdToStockQuantity[it.id]
                )
            }.toList()
        )
    }

    private fun mapToProductSearchResponse(product: Product, stockQuantity: BigDecimal?): ProductSearchResponse {
        return ProductSearchResponse(
            id = product.id,
            title = product.title,
            type = product.type,
            price = product.price,
            order = product.order,
            stockQuantity = stockQuantity.toIntDown(),
            stockQuantityThreshold = product.stockQuantityThreshold
        )
    }
}
