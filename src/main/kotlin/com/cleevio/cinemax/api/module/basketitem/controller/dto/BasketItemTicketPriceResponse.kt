package com.cleevio.cinemax.api.module.basketitem.controller.dto

import com.cleevio.cinemax.api.module.pricecategoryitem.constant.PriceCategoryItemNumber
import java.math.BigDecimal
import java.util.UUID

data class BasketItemTicketPriceResponse(
    val id: UUID,
    val basePrice: BigDecimal,
    val basePriceItemNumber: PriceCategoryItemNumber,
    val basePriceBeforeDiscount: BigDecimal,
    val surchargesAndFeesSum: BigDecimal,
    val totalPrice: BigDecimal,
)
