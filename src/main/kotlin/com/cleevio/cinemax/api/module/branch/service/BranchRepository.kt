package com.cleevio.cinemax.api.module.branch.service

import com.cleevio.cinemax.api.module.branch.entity.Branch
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface BranchRepository : JpaRepository<Branch, UUID> {

    fun findByCode(code: String): Branch?

    fun existsByCode(code: String): Boolean

    fun findByOriginalId(originalId: Int): Branch?

    fun existsByProductSalesCodeAndOriginalIdNot(productSalesCode: Int, originalId: Int): Boolean

    @Query(
        """
        SELECT b.* 
        FROM branch b 
        WHERE :auditoriumCode LIKE CONCAT(auditorium_original_code_prefix, '%')
        """,
        nativeQuery = true
    )
    fun findByAuditoriumOriginalCodePrefixLike(auditoriumCode: String): Branch?
}
