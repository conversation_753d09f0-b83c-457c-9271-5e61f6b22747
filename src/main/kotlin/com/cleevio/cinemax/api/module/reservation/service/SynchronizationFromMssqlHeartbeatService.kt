package com.cleevio.cinemax.api.module.reservation.service

import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.exception.HeartbeatCheckFailedException
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import java.time.Duration
import java.time.LocalDateTime

/**
 * Custom heartbeat solution. When an implementation of AbstractMssqlSynchronizationService hangs (happened with
 * ReservationMssqlSynchronizationService twice so far), either with exception or silently, it will stop updating its heartbeat
 * timestamp through SynchronizationFromMssqlService$updateLastHeartbeat. This service picks this last heartbeat timestamp and
 * compares it with custom settings persisted in HEARTBEAT_TIMEOUTS. If the heartbeat fails, an exception is thrown which
 * triggers a Sentry alert (in production only).
 *
 * For the heartbeat to work properly, the setting in HEARTBEAT_TIMEOUTS should be higher than the interval of synchronization.
 * E.g. in case of Reservation sync - 5 sec/1 min.
 *
 * Yes, I'm aware that this function's cron might also hang. This is a temporary solution until we have proper monitoring setup.
 */
@Service
class SynchronizationFromMssqlHeartbeatService(
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,

    @Value("\${features.synchronization-from-mssql.reservation-heartbeat.duration}")
    private val reservationHeartbeatDuration: Duration,
) {
    private val log = logger()

    fun checkLastReservationHeartbeat() = checkLastHeartbeat(SynchronizationFromMssqlType.RESERVATION)

    private fun checkLastHeartbeat(type: SynchronizationFromMssqlType) {
        synchronizationFromMssqlFinderService.findLastHeartbeatByType(type)?.let { lastHeartbeat ->
            if (lastHeartbeat.isBefore(LocalDateTime.now().minus(syncTypeToHeartbeatDuration[type]))) {
                log.error("Heartbeat check failed for type=$type.")
                throw HeartbeatCheckFailedException()
            }
        }
    }

    private val syncTypeToHeartbeatDuration = mapOf(
        SynchronizationFromMssqlType.RESERVATION to reservationHeartbeatDuration
    )
}
