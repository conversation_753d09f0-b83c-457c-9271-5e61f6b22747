package com.cleevio.cinemax.api.module.auditorium.service

import com.cleevio.cinemax.api.common.config.CinemaxConfigProperties
import com.cleevio.cinemax.api.common.constant.AUDITORIUM
import com.cleevio.cinemax.api.common.service.AbstractMssqlSynchronizationService
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.auditorium.constant.AuditoriumLockValues.SYNCHRONIZE_FROM_MSSQL
import com.cleevio.cinemax.api.module.auditorium.service.command.CreateOrUpdateAuditoriumCommand
import com.cleevio.cinemax.api.module.auditoriumlayout.service.AuditoriumLayoutService
import com.cleevio.cinemax.api.module.auditoriumlayout.service.DEFAULT_AUDITORIUM_LAYOUT_CODE
import com.cleevio.cinemax.api.module.auditoriumlayout.service.command.CreateOrUpdateAuditoriumLayoutCommand
import com.cleevio.cinemax.api.module.branch.service.BranchJpaFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.constant.SynchronizationFromMssqlType
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlFinderService
import com.cleevio.cinemax.api.module.synchronizationfrommssql.service.SynchronizationFromMssqlService
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.Tables.RKIN
import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rkin
import com.cleevio.library.lockinghandler.service.TryLock
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.UUID

@Service
class AuditoriumMssqlSynchronizationService(
    private val auditoriumService: AuditoriumService,
    private val auditoriumMssqlFinderRepository: AuditoriumMssqlFinderRepository,
    private val auditoriumLayoutService: AuditoriumLayoutService,
    private val branchJpaFinderService: BranchJpaFinderService,
    private val synchronizationFromMssqlFinderService: SynchronizationFromMssqlFinderService,
    private val cinemaxConfigProperties: CinemaxConfigProperties,
    synchronizationFromMssqlService: SynchronizationFromMssqlService,
) : AbstractMssqlSynchronizationService<Rkin>(
    mssqlEntityName = RKIN.name,
    synchronizationFromMssqlService = synchronizationFromMssqlService,
    synchronizationType = SynchronizationFromMssqlType.AUDITORIUM
) {
    private val log = logger()

    @TryLock(AUDITORIUM, SYNCHRONIZE_FROM_MSSQL)
    fun synchronizeAll() = synchronizeAll {
        auditoriumMssqlFinderRepository.findAllByUpdatedAtGt(
            updatedAt = synchronizationFromMssqlFinderService.findByType(SynchronizationFromMssqlType.AUDITORIUM)
        )
    }

    override fun validateAndPersist(mssqlEntity: Rkin) {
        if (mssqlEntity.nazev.isBlank()) {
            log.info("MSSQL ${RKIN.name} originalId=${mssqlEntity.rkinid} has blank title, skipping.")
            return
        }

        val branch = branchJpaFinderService.findByAuditoriumOriginalCodePrefixLike(
            auditoriumOriginalCode = mssqlEntity.csalu.trim()
        )

        val auditoriumId = auditoriumService.syncCreateOrUpdateAuditorium(
            mapToCreateOrUpdateAuditoriumCommand(
                mssqlAuditorium = mssqlEntity,
                branchId = branch?.id
            )
        )

        if (cinemaxConfigProperties.isHeadquartersDeployment()) {
            if (!auditoriumMssqlFinderRepository.existsDefaultAuditoriumLayout(mssqlEntity.csalu.trim())) {
                log.info("Default auditorium layout not found for Auditorium code=${mssqlEntity.csalu.trim()}. Creating one.")
                auditoriumLayoutService.syncCreateOrUpdateAuditoriumLayout(
                    CreateOrUpdateAuditoriumLayoutCommand(
                        originalId = mssqlEntity.rkinid + 100,
                        auditoriumId = auditoriumId,
                        code = DEFAULT_AUDITORIUM_LAYOUT_CODE,
                        title = "Základní"
                    )
                )
            }
        }
    }

    private fun mapToCreateOrUpdateAuditoriumCommand(
        mssqlAuditorium: Rkin,
        branchId: UUID?,
    ) = CreateOrUpdateAuditoriumCommand(
        originalId = mssqlAuditorium.rkinid,
        originalCode = mssqlAuditorium.csalu.toInt(),
        branchId = branchId,
        title = mssqlAuditorium.nazev.trimEnd(),
        capacity = mssqlAuditorium.kapacita.toInt(),
        city = mssqlAuditorium.misto.trim(),
        // Surcharges and fees are non-nullable on entity -> default values are set here for convenience to enable non-nullability
        // in command as well. In ScreeningService#syncCreateOrUpdate, these attrs are not processed.
        saleTimeLimit = 0,
        surchargeVip = BigDecimal.ZERO,
        surchargePremium = BigDecimal.ZERO,
        surchargeImax = BigDecimal.ZERO,
        surchargeUltraX = BigDecimal.ZERO,
        surchargeDBox = BigDecimal.ZERO,
        serviceFeeVip = BigDecimal.ZERO,
        serviceFeePremium = BigDecimal.ZERO,
        serviceFeeImax = BigDecimal.ZERO,
        serviceFeeUltraX = BigDecimal.ZERO,
        proCommission = 0,
        filmFondCommission = 0,
        distributorCommission = 0,
        publishOnline = false
    )
}
