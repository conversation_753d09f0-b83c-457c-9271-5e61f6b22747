package com.cleevio.cinemax.api.module.ticketdiscount.service

import com.cleevio.cinemax.api.common.service.JooqFinderRepository
import com.cleevio.cinemax.api.common.service.query.SearchQueryDeprecated
import com.cleevio.cinemax.api.common.util.paginationAndSorting
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import com.cleevio.cinemax.api.module.ticketdiscount.service.query.TicketDiscountFilter
import com.cleevio.cinemax.psql.tables.TicketDiscount.TICKET_DISCOUNT
import org.jooq.Condition
import org.jooq.DSLContext
import org.jooq.impl.DSL.count
import org.jooq.impl.DSL.trueCondition
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
class TicketDiscountJooqFinderRepository(
    private val psqlDslContext: DSLContext,
) : JooqFinderRepository<TicketDiscount> {

    override fun findById(id: UUID): TicketDiscount? {
        return psqlDslContext
            .selectFrom(TICKET_DISCOUNT)
            .where(TICKET_DISCOUNT.ID.eq(id))
            .fetchOneInto(TicketDiscount::class.java)
    }

    override fun findAll(): List<TicketDiscount> {
        return psqlDslContext
            .selectFrom(TICKET_DISCOUNT)
            .fetchInto(TicketDiscount::class.java)
    }

    fun findAllNonDeletedByIdIn(ids: Set<UUID>): List<TicketDiscount> {
        return psqlDslContext
            .selectFrom(TICKET_DISCOUNT)
            .where(TICKET_DISCOUNT.ID.`in`(ids))
            .and(TICKET_DISCOUNT.DELETED_AT.isNull)
            .fetchInto(TicketDiscount::class.java)
    }

    fun findNonDeletedById(id: UUID): TicketDiscount? {
        return psqlDslContext
            .selectFrom(TICKET_DISCOUNT)
            .where(TICKET_DISCOUNT.ID.eq(id))
            .and(TICKET_DISCOUNT.DELETED_AT.isNull)
            .fetchOneInto(TicketDiscount::class.java)
    }

    fun findNonDeletedAndValidById(id: UUID): TicketDiscount? {
        return psqlDslContext
            .selectFrom(TICKET_DISCOUNT)
            .where(TICKET_DISCOUNT.ID.eq(id))
            .and(TICKET_DISCOUNT.ACTIVE.eq(true)) // .and(TICKET_DISCOUNT.VOUCHER_ONLY.eq(false))
            .and(TICKET_DISCOUNT.DELETED_AT.isNull)
            .fetchOneInto(TicketDiscount::class.java)
    }

    fun findNonDeletedByOriginalId(originalId: Int): TicketDiscount? {
        return psqlDslContext
            .selectFrom(TICKET_DISCOUNT)
            .where(TICKET_DISCOUNT.ORIGINAL_ID.eq(originalId))
            .and(TICKET_DISCOUNT.DELETED_AT.isNull)
            .fetchOneInto(TicketDiscount::class.java)
    }

    fun findNonDeletedByCode(code: String): TicketDiscount? {
        return psqlDslContext
            .selectFrom(TICKET_DISCOUNT)
            .where(TICKET_DISCOUNT.CODE.eq(code))
            .and(TICKET_DISCOUNT.DELETED_AT.isNull)
            .fetchOneInto(TicketDiscount::class.java)
    }

    fun searchRowsCount(query: SearchQueryDeprecated<TicketDiscountFilter>): Int? {
        return psqlDslContext
            .selectCount()
            .from(TICKET_DISCOUNT)
            .where(selectCondition(query))
            .and(TICKET_DISCOUNT.DELETED_AT.isNull)
            .fetchOne(count())
    }

    fun searchRows(query: SearchQueryDeprecated<TicketDiscountFilter>): List<TicketDiscount> {
        return psqlDslContext
            .selectFrom(TICKET_DISCOUNT)
            .where(selectCondition(query))
            .and(TICKET_DISCOUNT.DELETED_AT.isNull)
            .paginationAndSorting(query.pageable, TICKET_DISCOUNT)
            .fetchInto(TicketDiscount::class.java)
    }

    private fun selectCondition(query: SearchQueryDeprecated<TicketDiscountFilter>): Condition {
        if (query.filter.usageTypes.isNullOrEmpty()) {
            return trueCondition()
        }
        return TICKET_DISCOUNT.USAGE_TYPE.`in`(query.filter.usageTypes)
    }
}
