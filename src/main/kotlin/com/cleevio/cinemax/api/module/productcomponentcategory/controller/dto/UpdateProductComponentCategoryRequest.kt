package com.cleevio.cinemax.api.module.productcomponentcategory.controller.dto

import com.cleevio.cinemax.api.module.productcomponentcategory.service.command.CreateOrUpdateProductComponentCategoryCommand
import java.util.UUID

data class UpdateProductComponentCategoryRequest(
    val title: String,
    val taxRate: Int,
) {
    fun toCommand(productComponentCategoryId: UUID) = CreateOrUpdateProductComponentCategoryCommand(
        id = productComponentCategoryId,
        title = title,
        taxRate = taxRate
    )
}
