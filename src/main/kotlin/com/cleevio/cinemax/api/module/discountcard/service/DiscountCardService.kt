package com.cleevio.cinemax.api.module.discountcard.service

import com.cleevio.cinemax.api.common.constant.DISCOUNT_CARD
import com.cleevio.cinemax.api.common.util.logger
import com.cleevio.cinemax.api.module.basket.event.BasketItemModifiedEvent
import com.cleevio.cinemax.api.module.basket.service.BasketDiscountCardService
import com.cleevio.cinemax.api.module.basket.service.BasketJpaFinderService
import com.cleevio.cinemax.api.module.basket.service.command.DeleteDiscountCardDiscountsAndUsageCommand
import com.cleevio.cinemax.api.module.basket.util.validateModifiableBasketState
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardLockValues.ACTIVATE_DISCOUNT_CARD
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardLockValues.CREATE_OR_UPDATE_DISCOUNT_CARD
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardLockValues.DEACTIVATE_DISCOUNT_CARD
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardLockValues.VALIDATE_DISCOUNT_CARD
import com.cleevio.cinemax.api.module.discountcard.constant.DiscountCardType
import com.cleevio.cinemax.api.module.discountcard.entity.DiscountCard
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardAlreadyActivatedOnAnotherPosException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardAlreadyActivatedOnCurrentPosException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardIsNotValidException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountCardNotFoundException
import com.cleevio.cinemax.api.module.discountcard.exception.DiscountVoucherAlreadyUsedException
import com.cleevio.cinemax.api.module.discountcard.service.command.ActivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.CreateOrUpdateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.DeactivateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcard.service.command.ValidateDiscountCardCommand
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageJpaFinderService
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageService
import com.cleevio.cinemax.api.module.discountcardusage.service.command.CreateDiscountCardUsageCommand
import com.cleevio.cinemax.api.module.posconfiguration.service.PosConfigurationJpaFinderService
import com.cleevio.cinemax.api.module.table.event.TableBasketItemModifiedEvent
import com.cleevio.library.lockinghandler.service.Lock
import com.cleevio.library.lockinghandler.service.LockFieldParameter
import jakarta.validation.Valid
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import org.springframework.transaction.annotation.Transactional
import org.springframework.validation.annotation.Validated
import java.util.UUID

@Service
@Validated
class DiscountCardService(
    private val discountCardJpaFinderService: DiscountCardJpaFinderService,
    private val discountCardRepository: DiscountCardRepository,
    private val discountCardUsageJpaFinderService: DiscountCardUsageJpaFinderService,
    private val discountCardUsageService: DiscountCardUsageService,
    private val posConfigurationJpaFinderService: PosConfigurationJpaFinderService,
    private val basketJpaFinderService: BasketJpaFinderService,
    private val basketDiscountCardService: BasketDiscountCardService,
    private val applicationEventPublisher: ApplicationEventPublisher,
) {
    private val log = logger()

    @Transactional
    @Lock(DISCOUNT_CARD, CREATE_OR_UPDATE_DISCOUNT_CARD)
    fun createOrUpdateDiscountCard(
        @Valid
        @LockFieldParameter("originalId")
        command: CreateOrUpdateDiscountCardCommand,
    ) {
        log.debug("Attempting to create or update discount card with code=${command.code}, provided command is: $command.")

        // legacy DB has vouchers in two DBs and has generated int primary key at the same time, so we have to use 'code' here
        val discountCard = discountCardRepository.findByCode(command.code)
        discountCard?.let {
            val updated = discountCardRepository.save(mapToExistingDiscountCard(it, command))
            log.debug("Updated discount card ${updated.id} (${updated.originalId}).")
        } ?: run {
            val created = discountCardRepository.save(mapToNewDiscountCard(command))
            log.debug("Created discount card ${created.id} (${created.originalId}).")
        }
    }

    @Transactional
    @Lock(DISCOUNT_CARD, VALIDATE_DISCOUNT_CARD)
    fun validateDiscountCard(
        @Valid
        @LockFieldParameter("discountCardCode")
        command: ValidateDiscountCardCommand,
    ): DiscountCard {
        val discountCard = discountCardRepository.findByCode(command.discountCardCode)?.also {
            if (!it.isValid()) {
                log.debug("Discount ${it.type.name.lowercase()} '${it.title}' with code ${it.code} is not valid.")
                throw DiscountCardIsNotValidException()
            }
        } ?: run {
            if (command.discountCardCode.length >= LOGGABLE_DISCOUNT_CARD_CODE_LENGTH) {
                log.debug("Discount card with code ${command.discountCardCode} was not found.")
            }
            throw DiscountCardNotFoundException()
        }

        basketJpaFinderService.getNonDeletedById(command.basketId).also { validateModifiableBasketState(it) }
        posConfigurationJpaFinderService.getById(command.posConfigurationId)

        validateDiscountCardIsNotAlreadyApplied(
            discountCard = discountCard,
            posConfigurationId = command.posConfigurationId
        )

        if (discountCard.type == DiscountCardType.VOUCHER) {
            discountCardUsageJpaFinderService.findNonDeletedByDiscountCardIdAndBasketIsPaidCount(discountCard.id)?.let {
                if (it == VOUCHER_BASKET_PAID_USAGE_LIMIT) {
                    log.debug(
                        "Discount voucher '${discountCard.title}' with code ${discountCard.code} was already used in the past."
                    )
                    throw DiscountVoucherAlreadyUsedException()
                }
            }
        }

        return discountCard
    }

    @Transactional
    @Lock(DISCOUNT_CARD, ACTIVATE_DISCOUNT_CARD)
    fun activateDiscountCard(
        @Valid
        @LockFieldParameter("discountCardId")
        command: ActivateDiscountCardCommand,
    ): DiscountCard {
        val discountCard = discountCardJpaFinderService.getById(command.discountCardId).also {
            if (isVipCardAndActivatedOnCurrentPos(it, command.posConfigurationId)) return it
        }

        discountCardUsageService.createDiscountCardUsage(
            CreateDiscountCardUsageCommand(
                discountCardId = command.discountCardId,
                screeningId = command.screeningId,
                basketId = command.basketId,
                posConfigurationId = command.posConfigurationId
            )
        )
        return discountCard
    }

    @Transactional
    @Lock(DISCOUNT_CARD, DEACTIVATE_DISCOUNT_CARD)
    fun deactivateDiscountCard(
        @Valid
        @LockFieldParameter("discountCardId")
        command: DeactivateDiscountCardCommand,
    ) {
        val basket = basketJpaFinderService.getNonDeletedById(command.basketId).also {
            validateModifiableBasketState(it)
        }
        discountCardJpaFinderService.getById(command.discountCardId).also {
            basketDiscountCardService.deleteAllDiscountCardDiscountsAppliedInBasketAndDiscountCardUsage(
                DeleteDiscountCardDiscountsAndUsageCommand(
                    basketId = command.basketId,
                    discountCardId = it.id,
                    discountCardCode = it.code,
                    discountCardTicketDiscountId = it.ticketDiscountId
                )
            )

            applicationEventPublisher.publishEvent(BasketItemModifiedEvent(basket.id))
            basket.tableId?.let { tableId ->
                applicationEventPublisher.publishEvent(TableBasketItemModifiedEvent(basketId = basket.id, tableId = tableId))
            }
        }
    }

    private fun mapToExistingDiscountCard(discountCard: DiscountCard, command: CreateOrUpdateDiscountCardCommand): DiscountCard {
        return discountCard.apply {
            command.ticketDiscountId?.let { ticketDiscountId = command.ticketDiscountId }
            command.productDiscountId?.let { productDiscountId = command.productDiscountId }
            command.productId?.let { productId = command.productId }
            type = command.type
            title = command.title
            code = command.code
            validFrom = command.validFrom
            validUntil = command.validUntil
            applicableToBasket = command.applicableToBasket
            applicableToScreening = command.applicableToScreening
            applicableToScreeningsPerDay = command.applicableToScreeningsPerDay
            command.productsCount?.let { productsCount = command.productsCount }
        }
    }

    private fun mapToNewDiscountCard(command: CreateOrUpdateDiscountCardCommand) = DiscountCard(
        id = command.id ?: UUID.randomUUID(),
        originalId = command.originalId,
        ticketDiscountId = command.ticketDiscountId,
        productDiscountId = command.productDiscountId,
        productId = command.productId,
        type = command.type,
        title = command.title,
        code = command.code,
        validFrom = command.validFrom,
        validUntil = command.validUntil,
        applicableToBasket = command.applicableToBasket,
        applicableToScreening = command.applicableToScreening,
        applicableToScreeningsPerDay = command.applicableToScreeningsPerDay,
        productsCount = command.productsCount
    )

    private fun validateDiscountCardIsNotAlreadyApplied(
        discountCard: DiscountCard,
        posConfigurationId: UUID,
    ) {
        if (discountCard.isVipCinemaxCard()) return

        discountCardUsageJpaFinderService.findNonDeletedByDiscountCardIdAndBasketIsInModifiableState(discountCard.id)?.let {
            if (it.posConfigurationId == posConfigurationId) {
                log.debug(
                    "Discount ${discountCard.type.name.lowercase()} '${discountCard.title}' with code ${discountCard.code} " +
                        "is already applied on current POS."
                )
                throw DiscountCardAlreadyActivatedOnCurrentPosException()
            } else {
                log.debug(
                    "Discount ${discountCard.type.name.lowercase()} '${discountCard.title}' with code ${discountCard.code} " +
                        "is already applied on another POS."
                )
                throw DiscountCardAlreadyActivatedOnAnotherPosException()
            }
        }
    }

    private fun isVipCardAndActivatedOnCurrentPos(
        discountCard: DiscountCard,
        posConfigurationId: UUID,
    ): Boolean =
        discountCard.isVipCinemaxCard() &&
            discountCardUsageJpaFinderService.isDiscountCardActivatedOnCurrentPos(
                discountCardId = discountCard.id,
                posConfigurationId = posConfigurationId
            )
}

private const val VOUCHER_BASKET_PAID_USAGE_LIMIT = 1
private const val LOGGABLE_DISCOUNT_CARD_CODE_LENGTH = 7 // the shortest code length of Online vouchers
