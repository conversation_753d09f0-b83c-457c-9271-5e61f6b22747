package com.cleevio.cinemax.api.module.auditoriumdefault.exception

import com.cleevio.cinemax.api.common.exception.ErrorType

enum class AuditoriumDefaultErrorType(
    override val code: String,
    override val message: String? = null,
) : ErrorType {
    AUDITORIUM_DEFAULT_ALREADY_EXISTS("100", "Auditorium default already exists."),
    AUDITORIUM_DEFAULT_NOT_FOUND("101", "Auditorium default not found."),
}
