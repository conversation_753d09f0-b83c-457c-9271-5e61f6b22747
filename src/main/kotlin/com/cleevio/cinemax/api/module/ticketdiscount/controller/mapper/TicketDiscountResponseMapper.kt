package com.cleevio.cinemax.api.module.ticketdiscount.controller.mapper

import com.cleevio.cinemax.api.common.util.isGreaterThanOrEqual
import com.cleevio.cinemax.api.common.util.mapToSet
import com.cleevio.cinemax.api.module.discountcard.service.DiscountCardJooqFinderService
import com.cleevio.cinemax.api.module.discountcardusage.entity.DiscountCardUsage
import com.cleevio.cinemax.api.module.discountcardusage.service.DiscountCardUsageFinderService
import com.cleevio.cinemax.api.module.ticket.service.TicketJooqFinderService
import com.cleevio.cinemax.api.module.ticketdiscount.constant.TicketDiscountUsageType
import com.cleevio.cinemax.api.module.ticketdiscount.controller.dto.TicketDiscountSearchResponse
import com.cleevio.cinemax.api.module.ticketdiscount.entity.TicketDiscount
import org.springframework.stereotype.Component
import java.util.UUID

@Component
class TicketDiscountResponseMapper(
    private val ticketJooqFinderService: TicketJooqFinderService,
    private val discountCardUsageFinderService: DiscountCardUsageFinderService,
    private val discountCardJooqFinderService: DiscountCardJooqFinderService,
) {

    fun mapList(
        ticketDiscounts: List<TicketDiscount>,
        basketId: UUID? = null,
        includeVoucherOnly: Boolean = false,
    ): List<TicketDiscountSearchResponse> {
        val tickets = basketId?.let { ticketJooqFinderService.findAllNonDeletedByBasketId(it).toSet() } ?: setOf()
        val primaryTicketDiscountCounts = tickets.mapNotNull { it.ticketDiscountPrimaryId }.groupingBy { it }.eachCount()
        val secondaryTicketDiscountCounts = tickets.mapNotNull { it.ticketDiscountSecondaryId }.groupingBy { it }.eachCount()
        val discountCardUsages = basketId?.let { discountCardUsageFinderService.findAllNonDeletedByBasketId(it).toSet() }
            ?: setOf()

        return ticketDiscounts
            .filter { createFilter(it, includeVoucherOnly) } // TODO https://cleevio.atlassian.net/browse/CMX-407
            .sortedWith(compareBy({ it.usageType.ordinal }, { it.order }, { it.title }))
            .map {
                val basketCount = when (it.usageType) {
                    TicketDiscountUsageType.PRIMARY -> primaryTicketDiscountCounts[it.id]
                    TicketDiscountUsageType.SECONDARY -> secondaryTicketDiscountCounts[it.id]
                }

                TicketDiscountSearchResponse(
                    id = it.id,
                    title = it.title,
                    type = it.type,
                    usageType = it.usageType,
                    amount = it.amount,
                    percentage = it.percentage,
                    availableForSelection = basketId?.let { _ ->
                        determineTicketDiscountSelectionAvailability(
                            ticketDiscount = it,
                            basketCount = basketCount,
                            discountCardUsages = discountCardUsages
                        )
                    } ?: true,
                    availableForBasket = basketId?.let { _ ->
                        determineTicketDiscountBasketAvailability(
                            ticketDiscount = it,
                            basketCount = basketCount
                        )
                    } ?: true
                )
            }
    }

    private fun determineTicketDiscountSelectionAvailability(
        ticketDiscount: TicketDiscount,
        basketCount: Int?,
        discountCardUsages: Set<DiscountCardUsage>,
    ): Boolean {
        // TODO improve - fetch all results into a map and pass them to this function for evaluation
        if (discountCardUsages.isNotEmpty()) {
            val discountCardIds = discountCardUsages.mapToSet { it.discountCardId }
            val discountCards = discountCardJooqFinderService.findAllByIdIn(discountCardIds).toSet()
            val ticketDiscountIdsFromCards = discountCards.mapToSet { it.ticketDiscountId }
            if (ticketDiscountIdsFromCards.contains(ticketDiscount.id)) {
                discountCards.filter { it.ticketDiscountId == ticketDiscount.id }.sumOf { it.applicableToBasket ?: 0 }.let {
                    return (basketCount ?: 0) < it
                }
            }
        }

        return determineTicketDiscountBasketAvailability(ticketDiscount = ticketDiscount, basketCount = basketCount)
    }

    private fun determineTicketDiscountBasketAvailability(
        ticketDiscount: TicketDiscount,
        basketCount: Int?,
    ): Boolean {
        val applicableToCountExceeded = basketCount.isGreaterThanOrEqual(ticketDiscount.applicableToCount)
        return !applicableToCountExceeded
    }

    private fun createFilter(ticketDiscount: TicketDiscount, includeVoucherOnly: Boolean): Boolean {
        if (includeVoucherOnly) return ticketDiscount.active

        return ticketDiscount.active && !ticketDiscount.voucherOnly
    }
}
