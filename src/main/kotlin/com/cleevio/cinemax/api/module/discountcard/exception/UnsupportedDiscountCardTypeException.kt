package com.cleevio.cinemax.api.module.discountcard.exception

import com.cleevio.cinemax.api.common.exception.ApiException
import org.springframework.http.HttpStatus
import org.springframework.web.bind.annotation.ResponseStatus

@ResponseStatus(value = HttpStatus.UNPROCESSABLE_ENTITY)
class UnsupportedDiscountCardTypeException : ApiException(
    Module.DISCOUNT_CARD,
    DiscountCardErrorType.UNSUPPORTED_DISCOUNT_CARD_TYPE
)
