package com.cleevio.cinemax.api.module.distributor.event

import com.cleevio.cinemax.api.common.integration.pubsub.constant.MessageType
import com.cleevio.cinemax.api.common.integration.pubsub.dto.MessagePayload
import com.cleevio.cinemax.api.common.util.toJsonString
import com.cleevio.cinemax.api.module.distributor.entity.Distributor
import com.fasterxml.jackson.annotation.JsonInclude
import java.util.Optional
import java.util.UUID

@JsonInclude(JsonInclude.Include.NON_NULL)
data class AdminDistributorCreatedOrUpdatedEvent(
    val code: String,
    val disfilmCode: Optional<String>? = null,
    val title: String,
    val addressStreet: Optional<String>? = null,
    val addressCity: Optional<String>? = null,
    val addressPostCode: Optional<String>? = null,
    val contactName1: Optional<String>? = null,
    val contactName2: Optional<String>? = null,
    val contactName3: Optional<String>? = null,
    val contactPhone1: Optional<String>? = null,
    val contactPhone2: Optional<String>? = null,
    val contactPhone3: Optional<String>? = null,
    val contactEmails: Optional<Set<String>>? = null,
    val bankName: Optional<String>? = null,
    val bankAccount: Optional<String>? = null,
    val idNumber: Optional<String>? = null,
    val taxIdNumber: Optional<String>? = null,
    val vatRate: Optional<Int>? = null,
    val note: Optional<String>? = null,
) {
    fun toMessagePayload() = MessagePayload(
        id = UUID.randomUUID(),
        type = MessageType.DISTRIBUTOR_CREATED_OR_UPDATED,
        data = this.toJsonString()
    )
}

fun Distributor.toMessagingEvent() = AdminDistributorCreatedOrUpdatedEvent(
    code = this.code,
    disfilmCode = Optional.ofNullable(this.disfilmCode),
    title = this.title,
    addressStreet = Optional.ofNullable(this.addressStreet),
    addressCity = Optional.ofNullable(this.addressCity),
    addressPostCode = Optional.ofNullable(this.addressPostCode),
    contactName1 = Optional.ofNullable(this.contactName1),
    contactName2 = Optional.ofNullable(this.contactName2),
    contactName3 = Optional.ofNullable(this.contactName3),
    contactPhone1 = Optional.ofNullable(this.contactPhone1),
    contactPhone2 = Optional.ofNullable(this.contactPhone2),
    contactPhone3 = Optional.ofNullable(this.contactPhone3),
    contactEmails = Optional.ofNullable(this.contactEmails),
    bankName = Optional.ofNullable(this.bankName),
    bankAccount = Optional.ofNullable(this.bankAccount),
    idNumber = Optional.ofNullable(this.idNumber),
    taxIdNumber = Optional.ofNullable(this.taxIdNumber),
    vatRate = Optional.ofNullable(this.vatRate),
    note = Optional.ofNullable(this.note)
)
