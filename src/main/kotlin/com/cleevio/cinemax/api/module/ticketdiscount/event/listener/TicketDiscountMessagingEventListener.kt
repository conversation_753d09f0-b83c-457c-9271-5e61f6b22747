package com.cleevio.cinemax.api.module.ticketdiscount.event.listener

import com.cleevio.cinemax.api.common.service.PublisherService
import com.cleevio.cinemax.api.module.ticketdiscount.event.AdminTicketDiscountCreatedOrUpdatedEvent
import com.cleevio.cinemax.api.module.ticketdiscount.event.AdminTicketDiscountDeletedEvent
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty
import org.springframework.stereotype.Component
import org.springframework.transaction.event.TransactionPhase
import org.springframework.transaction.event.TransactionalEventListener

@Component
@ConditionalOnProperty(
    "spring.cloud.gcp.pubsub.enabled",
    havingValue = "true"
)
class TicketDiscountMessagingEventListener(
    private val publisherService: PublisherService,
) {
    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    fun listenToAdminTicketDiscountCreatedOrUpdatedEvent(event: AdminTicketDiscountCreatedOrUpdatedEvent) {
        publisherService.publish(messagePayload = event.toMessagePayload())
    }

    @TransactionalEventListener(phase = TransactionPhase.BEFORE_COMMIT)
    fun listenToAdminTicketDiscountDeletedEvent(event: AdminTicketDiscountDeletedEvent) {
        publisherService.publish(messagePayload = event.toMessagePayload())
    }
}
