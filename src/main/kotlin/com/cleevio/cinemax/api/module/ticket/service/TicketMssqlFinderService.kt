package com.cleevio.cinemax.api.module.ticket.service

import com.cleevio.cinemax.mssql.rps_cinemaxe.dbo.tables.pojos.Rlistky
import org.springframework.stereotype.Service

@Service
class TicketMssqlFinderService(
    private val ticketMssqlFinderRepository: TicketMssqlFinderRepository,
) {

    fun findLatestByOriginalScreeningIdAndOriginalReservationId(originalScreeningId: Int, originalReservationId: Int): Rlistky? {
        return ticketMssqlFinderRepository.findLatestByOriginalScreeningIdAndOriginalReservationId(
            originalScreeningId = originalScreeningId,
            originalReservationId = originalReservationId
        )
    }
}
