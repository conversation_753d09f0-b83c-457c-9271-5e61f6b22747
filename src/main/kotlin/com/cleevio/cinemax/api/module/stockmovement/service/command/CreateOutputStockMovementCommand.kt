package com.cleevio.cinemax.api.module.stockmovement.service.command

import com.cleevio.cinemax.api.common.annotation.NullOrNotBlank
import com.cleevio.cinemax.api.module.stockmovement.constant.StockMovementType
import jakarta.validation.constraints.Positive
import java.math.BigDecimal
import java.time.LocalDateTime
import java.util.UUID

data class CreateOutputStockMovementCommand(
    val type: StockMovementType,
    val productComponentId: UUID,
    val recordedAt: LocalDateTime,

    @field:Positive
    val quantity: BigDecimal,

    @field:NullOrNotBlank
    val note: String? = null,

    val basketItemId: UUID? = null,
)
