package com.cleevio.cinemax.api.module.screeningtype.service

import com.cleevio.cinemax.api.module.screeningtype.entity.ScreeningType
import org.springframework.data.jpa.repository.JpaRepository
import org.springframework.data.jpa.repository.Query
import org.springframework.data.repository.query.Param
import org.springframework.stereotype.Repository
import java.util.UUID

@Repository
interface ScreeningTypeRepository : JpaRepository<ScreeningType, UUID> {

    fun existsAllByIdIn(screeningTypeIds: Set<UUID>): Boolean
    fun findAllByCodeIn(screeningTypeCodes: Set<String>): List<ScreeningType>
    fun findByOriginalId(originalId: Int): ScreeningType?
    fun existsByCode(code: String): Boolean
    fun findByCode(code: String): ScreeningType?
    fun findAllByIdIn(ids: Set<UUID>): List<ScreeningType>

    @Query(
        """
    SELECT st.id
    FROM auditorium_default ad, screening_type st
    LEFT JOIN screening_types sst ON sst.screening_type_id = st.id
        AND sst.screening_id = :screeningId
    WHERE ad.auditorium_id = :auditoriumId
        AND COALESCE(sst.blacklisted, FALSE) = FALSE
        AND (
            (
                (st.code = '$IMAX_SCREENING_TYPE_CODE' AND 
                    (ad.surcharge_imax > 0 
                     OR ad.service_fee_imax > 0)
                )
                OR 
                (st.code = '$VIP_SCREENING_TYPE_CODE' AND 
                    (ad.surcharge_vip > 0 
                     OR ad.service_fee_vip > 0)
                )
                OR 
                (st.code = '$ULTRA_X_SCREENING_TYPE_CODE' AND 
                    (ad.surcharge_ultra_x > 0 
                     OR ad.service_fee_ultra_x > 0)
                )
                OR 
                (st.code = '$D_BOX_SCREENING_TYPE_CODE' AND 
                    ad.surcharge_d_box > 0
                )
            )
            OR
            (
                st.id IN (:requestedScreeningTypeIds) 
                AND st.code NOT IN (
                    '$IMAX_SCREENING_TYPE_CODE', 
                    '$VIP_SCREENING_TYPE_CODE', 
                    '$ULTRA_X_SCREENING_TYPE_CODE', 
                    '$D_BOX_SCREENING_TYPE_CODE'
                )
            )
        )
    """,
        nativeQuery = true
    )
    fun findScreeningTypesByScreeningIdAndAuditoriumId(
        @Param("screeningId") screeningId: UUID,
        @Param("auditoriumId") auditoriumId: UUID,
        @Param("requestedScreeningTypeIds") requestedScreeningTypeIds: Set<UUID>,
    ): Set<UUID>
}
