package com.cleevio.cinemax.api.common.startup

import com.cleevio.cinemax.api.common.config.StorageConfigProperties
import com.cleevio.cinemax.api.common.util.logger
import jakarta.annotation.PostConstruct
import org.springframework.stereotype.Component
import java.nio.file.Files
import java.nio.file.Paths

@Component
class PersistentVolumeMountCheck(
    private val storageConfigProperties: StorageConfigProperties,
) {

    private val log = logger()

    /**
     * This method runs once on application startup.
     * It recursively checks the /pos directory for "out.xml" files
     * and logs either an error or an info message depending on the count.
     */
    @PostConstruct
    fun checkMountedDirectory() {
        val posDirectory = Paths.get(storageConfigProperties.persistentVolumeMountPath)
        if (!Files.exists(posDirectory) || !Files.isDirectory(posDirectory)) {
            log.error(
                "The ${storageConfigProperties.persistentVolumeMountPath} directory does not exist " +
                    "or is not accessible."
            )
            return
        }

        try {
            val xmlCount = Files.walk(posDirectory)
                .filter { path -> path.fileName.toString() == XML_FILENAME }
                .count()

            if (xmlCount == 0L) {
                log.error(
                    "No $XML_FILENAME files found under ${storageConfigProperties.persistentVolumeMountPath}. " +
                        "Persistent storage volume might not be mounted correctly."
                )
            } else {
                log.info(
                    "Found $xmlCount $XML_FILENAME file(s) under ${storageConfigProperties.persistentVolumeMountPath}. " +
                        "Storage volume seems to be mounted correctly."
                )
            }
        } catch (ex: Exception) {
            log.error(
                "An error occurred while scanning ${storageConfigProperties.persistentVolumeMountPath} " +
                    "for $XML_FILENAME",
                ex
            )
        }
    }
}

private const val XML_FILENAME = "out.xml"
