package com.cleevio.cinemax.api.common.util

import com.cleevio.cinemax.api.common.config.LocalDateTimeDeserializer
import com.cleevio.cinemax.api.common.config.LocalDateTimeSerializer
import com.cleevio.cinemax.api.common.util.jackson.BigDecimalSerializer
import com.fasterxml.jackson.databind.SerializationFeature
import com.fasterxml.jackson.databind.deser.std.NumberDeserializers
import com.fasterxml.jackson.databind.module.SimpleModule
import com.fasterxml.jackson.dataformat.xml.XmlMapper
import com.fasterxml.jackson.module.kotlin.registerKotlinModule
import java.math.BigDecimal
import java.time.LocalDateTime

fun <T> T.serializeToXml(): String {
    return XML_HEADER + System.lineSeparator() + XML_MAPPER.writeValueAsString(this)
}
fun <T> String.deserializeXml(clazz: Class<T>): T {
    return XML_MAPPER.readValue(this, clazz)
}

val XML_MAPPER: XmlMapper = initXmlMapper()

private fun initXmlMapper(): XmlMapper {
    val module = SimpleModule()
        .addSerializer(BigDecimal::class.java, BigDecimalSerializer())
        .addSerializer(LocalDateTime::class.java, LocalDateTimeSerializer())
        .addDeserializer(LocalDateTime::class.java, LocalDateTimeDeserializer())
        .addDeserializer(BigDecimal::class.java, NumberDeserializers.BigDecimalDeserializer())

    val xmlMapper = XmlMapper.builder()
        .addModule(module)
        .enable(SerializationFeature.INDENT_OUTPUT)
        .build()

    xmlMapper.registerKotlinModule()
    return xmlMapper
}

fun String.flattenXml() = replace(Regex(">\\s+<"), "><").trim()

private const val XML_HEADER = "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"no\"?>"
