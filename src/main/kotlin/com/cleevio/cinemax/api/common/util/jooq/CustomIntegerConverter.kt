package com.cleevio.cinemax.api.common.util.jooq

import org.jooq.Converter

class CustomIntegerConverter : Converter<Integer?, Integer?> {

    override fun from(databaseObject: Integer?): Integer? = databaseObject
    override fun to(userObject: Integer?): Integer? = userObject
    override fun fromType() = Integer::class.java as Class<Integer?>
    override fun toType() = Integer::class.java as Class<Integer?>
}
