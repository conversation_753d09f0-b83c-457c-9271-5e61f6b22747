package com.cleevio.cinemax.api.common.integration.web

import com.cleevio.cinemax.api.common.integration.web.config.CinemaxWebConfigProperties
import org.springframework.stereotype.Component

@Component
class CinemaxSecondaryWebConnector(
    cinemaxWebConfigProperties: CinemaxWebConfigProperties,
) : AbstractCinemaxWebConnector(
    baseUrl = cinemaxWebConfigProperties.secondaryBaseUrl,
    username = cinemaxWebConfigProperties.username,
    password = cinemaxWebConfigProperties.password
)
