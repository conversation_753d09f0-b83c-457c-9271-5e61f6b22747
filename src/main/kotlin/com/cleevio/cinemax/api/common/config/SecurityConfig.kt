package com.cleevio.cinemax.api.common.config

import com.cleevio.cinemax.api.common.constant.ACTUATOR_PATH
import com.cleevio.cinemax.api.common.constant.ANONYMOUS_USERNAME
import com.cleevio.cinemax.api.common.constant.API_DOCS_PATH
import com.cleevio.cinemax.api.common.constant.DBOX_SYSTEM_PATH
import com.cleevio.cinemax.api.common.constant.MANAGER_FILES_PATH
import com.cleevio.cinemax.api.common.constant.PUBLIC_PATHS
import com.cleevio.cinemax.api.common.constant.TMS_SYSTEM_SCHEDULE_PATH
import com.cleevio.cinemax.api.common.constant.VIP_APP_ANY_PATH
import com.cleevio.cinemax.api.common.constant.WEB_APP_ANY_PATH
import com.cleevio.cinemax.api.common.constant.WebSocketEndpoint.WEBSOCKETS_BASE_ENDPOINT
import com.cleevio.cinemax.api.common.filter.AcceptHeaderFilter
import com.cleevio.cinemax.api.common.filter.VipAppApiKeyFilter
import com.cleevio.cinemax.api.common.filter.WebAppApiKeyFilter
import org.springframework.context.annotation.Bean
import org.springframework.context.annotation.Configuration
import org.springframework.http.HttpMethod
import org.springframework.http.HttpStatus
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity
import org.springframework.security.config.annotation.web.builders.HttpSecurity
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity
import org.springframework.security.config.annotation.web.configuration.WebSecurityCustomizer
import org.springframework.security.config.http.SessionCreationPolicy
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder
import org.springframework.security.crypto.password.PasswordEncoder
import org.springframework.security.web.SecurityFilterChain
import org.springframework.security.web.authentication.HttpStatusEntryPoint
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter
import org.springframework.security.web.authentication.www.BasicAuthenticationFilter
import org.springframework.security.web.util.matcher.AntPathRequestMatcher
import org.springframework.web.cors.CorsConfiguration
import org.springframework.web.cors.CorsConfigurationSource
import org.springframework.web.cors.UrlBasedCorsConfigurationSource

@EnableWebSecurity
@EnableMethodSecurity
@Configuration
class SecurityConfig(
    private val jwtAuthenticationFilter: JwtAuthenticationFilter,
    private val webAppApiKeyFilter: WebAppApiKeyFilter,
    private val vipAppApiKeyFilter: VipAppApiKeyFilter,
) {

    @Bean
    fun passwordEncoder(): PasswordEncoder {
        return BCryptPasswordEncoder()
    }

    @Bean
    fun webSecurityCustomizer(): WebSecurityCustomizer {
        return WebSecurityCustomizer {
            it.ignoring().requestMatchers(
                API_DOCS_PATH,
                ACTUATOR_PATH,
                WEBSOCKETS_BASE_ENDPOINT,
                TMS_SYSTEM_SCHEDULE_PATH,
                DBOX_SYSTEM_PATH
            ).and().ignoring().requestMatchers(
                AntPathRequestMatcher(MANAGER_FILES_PATH, HttpMethod.GET.name()),
                AntPathRequestMatcher(TMS_SYSTEM_SCHEDULE_PATH, HttpMethod.GET.name()),
                AntPathRequestMatcher(DBOX_SYSTEM_PATH, HttpMethod.GET.name())
            )
        }
    }

    @Bean
    fun corsConfiguration(): CorsConfigurationSource {
        val config = CorsConfiguration().apply {
            allowedOriginPatterns = listOf("*")
            allowedHeaders = listOf("*")
            allowedMethods = listOf("*")
            applyPermitDefaultValues()
        }
        val source = UrlBasedCorsConfigurationSource().apply {
            registerCorsConfiguration("/**", config)
        }
        return source
    }

    @Bean
    fun securityFilterChain(http: HttpSecurity): SecurityFilterChain =
        http.csrf { it.disable() }
            .cors { it.configurationSource(corsConfiguration()) }
            .authorizeHttpRequests {
                it.requestMatchers(*PUBLIC_PATHS).permitAll()
                it.requestMatchers(WEB_APP_ANY_PATH).permitAll()
                it.requestMatchers(VIP_APP_ANY_PATH).permitAll()
                it.anyRequest().authenticated()
            }
            .exceptionHandling { it.authenticationEntryPoint(HttpStatusEntryPoint(HttpStatus.UNAUTHORIZED)) }
            .sessionManagement { it.sessionCreationPolicy(SessionCreationPolicy.STATELESS) }
            .addFilterAfter(AcceptHeaderFilter(), BasicAuthenticationFilter::class.java)
            .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter::class.java)
            .addFilterBefore(webAppApiKeyFilter, UsernamePasswordAuthenticationFilter::class.java)
            .addFilterBefore(vipAppApiKeyFilter, UsernamePasswordAuthenticationFilter::class.java)
            .anonymous { it.principal(ANONYMOUS_USERNAME) }
            .build()
}
